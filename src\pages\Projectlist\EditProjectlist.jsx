import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useTranslation } from "react-i18next";
// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

// React-icons
import { AiTwotoneCalendar } from "react-icons/ai";

function EditProjectlist() {
  const navigate = useNavigate();
  const { orderNo } = useParams();
  const location = useLocation();
  const { t } = useTranslation();
  const formContainerRef = useRef(null);
  const [scale, setScale] = useState(1);

  // Add refs for all datepickers
  const orderDateRef = useRef(null);
  const specifiedDeliveryDateRef = useRef(null);
  const scheduledShipDateRef = useRef(null);
  const processingCompletionDateRef = useRef(null);
  const specifiedDeliveryDate2Ref = useRef(null);
  const scheduledShipDate2Ref = useRef(null);

  // Form state initialized with project data or defaults
  const [formData, setFormData] = useState({
    orderNo: "",
    orderDate: null,
    specifiedDeliveryDate: null,
    specifiedDeliveryDate2: null,
    scheduledShipDate: null,
    scheduledShipDate2: null,
    depositCategory: "0",
    limitedExpress: "0",
    customer: "",
    rank: "",
    department: "",
    warehouse: "",
    managerCode: "",
    managerName: "",
    deliveryCode: "",
    deliveryName: "",
    transactionCode: "",
    transactionName: "",
    documentType: "",
    siteName: "",
    ProcessingCompletionDate: null,
    processStatus: [],
    grossProfitRate: 0.0,
    quoteNo: "",
    closingDate1: "",
    classification: "",
    productCode: "",
    productInfo: "",
    productDetails: "",
    productSpecs: "",
    productMeasurements: "",
    retailPrice: "",
    priceInfo: "",
    remarks: "",
    productName: "",
    taxPercentage: "",
    numberOfOrders: "",
    orderPrice: "",
    unitPriceUpdate: "",
    orderAmount: "",
    orderTaxAmount: "",
    unit: "",
    orderCount: "",
    costUnitPrice: "",
    costAmount: "",
    soldOut: false,
    shipmentComplete: false,
    dataEntryPerson: "",
    dataEntryDate: "",
    selectAll: false,
  });

  // Handle window resize and adjust scale
  useEffect(() => {
    const handleResize = () => {
      if (formContainerRef.current) {
        const containerWidth = formContainerRef.current.offsetWidth;
        const formWidth = 1024; // Your desired form width
        const newScale = Math.min(1, containerWidth / formWidth);
        setScale(newScale);
      }
    };

    handleResize(); // Initial calculation
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    const formattedValue =
      name === "grossProfitRate" ? parseFloat(value || 0).toFixed(2) : value;

    setFormData((prev) => ({
      ...prev,
      [name]: formattedValue,
    }));
  };

  const handleDateChange = (date, name) => {
    setFormData((prev) => ({
      ...prev,
      [name]: date,
    }));
  };

  const handleOpenDatePicker = (ref) => {
    if (ref.current) {
      ref.current.setOpen(true);
    }
  };

  const handleSave = () => {
    console.log("Updated form data:", formData);
    navigate("/project-list");
  };

  const handleCancel = () => {
    navigate("/project-list");
  };

  return (
    <div className="w-full projectlist-page">
      <div className="flex justify-between items-center mb-4">
        <h1
          data-testid="text-editFormProjectlist-projectlist"
          className="text-lg sm:text-xl font-bold"
        >
          Project List (Edit)
        </h1>
        <div className="flex gap-2">
          <SaveButton
            dataTestId="button-editFormProjectlist-saveProjectlist"
            onClick={handleSave}
          />
          <CancelButton
            dataTestId="button-editFormProjectlist-cancelProjectlist"
            onClick={handleCancel}
          />
        </div>
      </div>

      <div ref={formContainerRef} className="w-full">
        <div
          style={{
            transform: `scale(${scale})`,
            transformOrigin: "top left",
            transition: "transform 0.3s ease",
            width: `${(1 / scale) * 100}%`,
            height: `${(1 / scale) * 100}%`,
          }}
        >
          {/* First row - Order No. */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white p-1 min-w-[100px] text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-orderNo">
                    {t("project.order")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-orderNo"
                  type="text"
                  name="orderNo"
                  onChange={handleChange}
                  className="flex-1 min-w-[50px]"
                />
                <button
                  data-testid="button-editFormProjectlist-repeat"
                  name="repeat"
                  type="button"
                  className="bg-gray-200 hover:bg-gray-300 px-2 py-1 border-l border-black text-sm whitespace-nowrap min-w-[60px]"
                >
                  {t("project.repeat")}
                </button>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-orderDate">
                    {t("project.orderDate")}
                  </span>
                </div>
                <div className="relative">
                  <DatePicker
                    data-testid="datepicker-editFormProjectlist-orderDate"
                    ref={orderDateRef}
                    selected={formData.orderDate}
                    onChange={(date) => handleDateChange(date, "orderDate")}
                    popperPlacement="bottom-start"
                    portalId="root-portal"
                    inline={false}
                    dateFormat="dd/MM/yyyy"
                    className="flex-1 w-full h-full border-none outline-none px-2 pt-1 pr-7"
                    isClearable={formData.orderDate !== null}
                  />
                  {!formData.orderDate && (
                    <AiTwotoneCalendar
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                      size={18}
                      onClick={() => handleOpenDatePicker(orderDateRef)}
                    />
                  )}
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-2 text-center text-xs whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-deliveryDate">
                    {t("project.specDate")}
                  </span>
                </div>
                <div className="relative w-full">
                  <DatePicker
                    data-testid="datepicker-editFormProjectlist-deliveryDate"
                    ref={specifiedDeliveryDateRef}
                    selected={formData.specifiedDeliveryDate}
                    onChange={(date) =>
                      handleDateChange(date, "specifiedDeliveryDate")
                    }
                    popperPlacement="bottom-start"
                    portalId="root-portal"
                    inline={false}
                    dateFormat="dd/MM/yyyy"
                    className="flex-1 w-full h-full border-none outline-none px-2 pt-1 pr-7"
                    isClearable={formData.specifiedDeliveryDate !== null}
                  />
                  {!formData.specifiedDeliveryDate && (
                    <AiTwotoneCalendar
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                      size={18}
                      onClick={() =>
                        handleOpenDatePicker(specifiedDeliveryDateRef)
                      }
                    />
                  )}
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[130px] p-2 text-center text-xs whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-shipDate">
                    {t("project.shipDate")}
                  </span>
                </div>
                <div className="relative w-full">
                  <DatePicker
                    data-testid="datepicker-editFormProjectlist-shipDate"
                    ref={scheduledShipDateRef}
                    selected={formData.scheduledShipDate}
                    onChange={(date) =>
                      handleDateChange(date, "scheduledShipDate")
                    }
                    popperPlacement="bottom-start"
                    portalId="root-portal"
                    inline={false}
                    dateFormat="dd/MM/yyyy"
                    className="flex-1 w-full h-full border-none outline-none px-2 pt-1 pr-9"
                    isClearable={formData.scheduledShipDate !== null}
                  />
                  {!formData.scheduledShipDate && (
                    <AiTwotoneCalendar
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                      size={18}
                      onClick={() => handleOpenDatePicker(scheduledShipDateRef)}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Second row - Quote No. */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white p-1 min-w-[100px] text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-quoteNo">
                    {t("project.quoteNo")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-quoteNo"
                  type="text"
                  name="quoteNo"
                  value={formData.quoteNo}
                  onChange={handleChange}
                  className="flex-1 min-w-[50px]"
                />
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-customer">
                    {t("project.customer")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-customer"
                    type="text"
                    name="customer"
                    onChange={handleChange}
                    className="border-r border-black w-[40%]"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-customer"
                    type="text"
                    onChange={handleChange}
                    className="w-[60%]"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-rank">
                    {t("project.rank")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-rank"
                  type="text"
                  name="rank"
                  onChange={handleChange}
                  className="flex-1"
                />
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[130px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-closingDate1">
                    {t("project.closingDate1")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-closingDate1"
                    type="text"
                    name="closingDate1"
                    value={formData.closingDate1}
                    onChange={handleChange}
                    className="border-r border-black w-[40%]"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-closingDate1"
                    type="text"
                    onChange={handleChange}
                    className="w-[60%]"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Third row - Deposit Category*/}
          <div className="grid grid-cols-12 gap-1">
            {/* Deposit Category */}
            <div className="col-span-3">
              <div className="flex flex-col border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-sm p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-depositCategory">
                    {t("project.depositCategory")}
                  </span>
                </div>
                <div className="flex flex-1">
                  <label className="flex items-center justify-start px-1 bg-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-depositRadio1"
                      type="radio"
                      name="depositCategory"
                      value="0"
                      checked={formData.depositCategory === "0"}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-depositText0">
                      0
                    </span>
                  </label>
                  <label className="flex items-center justify-start flex-1 px-1 border-x border-black bg-purple-700 text-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-depositRadio2"
                      type="radio"
                      name="depositCategory"
                      value="0Normal"
                      checked={formData.depositCategory === "0Normal"}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-depositText0Normal">
                      {t("project.depositAmount0")}
                    </span>
                  </label>
                  <label className="flex items-center justify-start flex-1 px-1 bg-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-depositRadio3"
                      type="radio"
                      name="depositCategory"
                      value="1"
                      checked={formData.depositCategory === "1"}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-depositText1Deposit">
                      {t("project.depositAmount1")}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Manager */}
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-manager">
                    {t("project.manager")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-manager"
                    type="text"
                    name="managerCode"
                    onChange={handleChange}
                    className="border-r border-black w-[40%]"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-manager"
                    type="text"
                    name="managerName"
                    onChange={handleChange}
                    className="w-[60%]"
                  />
                </div>
              </div>
            </div>

            {/* Department */}
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-department">
                    {t("project.department")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-department"
                  type="text"
                  name="department"
                  onChange={handleChange}
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* Fourth row - Limited express */}
          <div className="grid grid-cols-12 gap-x-1">
            {/* Limited express classification */}
            <div className="col-span-3">
              <div className="flex flex-col border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-sm p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-limitedExpress">
                    {t("project.limitedExpressClass")}
                  </span>
                </div>
                <div className="flex flex-1">
                  <label className="flex items-center justify-start px-1 bg-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-limitedRadio1"
                      type="radio"
                      name="limitedExpress"
                      value="0"
                      checked={formData.limitedExpress === "0"}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-limitedText0">
                      0
                    </span>
                  </label>
                  <label className="flex items-center justify-start flex-1 px-1 border-x border-black bg-purple-700 text-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-limitedRadio2"
                      type="radio"
                      name="limitedExpress"
                      value="0None"
                      checked={formData.limitedExpress === "0None"}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-limitedText0None">
                      {t("project.none")}
                    </span>
                  </label>
                  <label className="flex items-center justify-start flex-1 px-1 bg-white text-sm">
                    <input
                      data-testid="radio-editFormProjectlist-limitedRadio3"
                      type="radio"
                      name="limitedExpress"
                      value="1"
                      checked={formData.limitedExpress === "1"}
                      onChange={handleChange}
                      className="mr-1 size-3"
                    />
                    <span data-testid="text-editFormProjectlist-limitedText1Yes">
                      {t("project.yes")}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Delivery destination */}
            <div className="col-span-3 -mt-4">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-sm min-w-[140px] p-1.5 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-deliveryDes">
                    {t("project.deliveryDestination")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-deliveryDes"
                    type="text"
                    name="deliveryCode"
                    onChange={handleChange}
                    className="border-r border-black w-[40%]"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-deliveryDes"
                    type="text"
                    name="deliveryName"
                    onChange={handleChange}
                    className="w-[60%]"
                  />
                </div>
              </div>
            </div>

            {/* Warehouse */}
            <div className="col-span-3 -mt-4">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-warehouse">
                    {t("project.warehouse")}
                  </span>
                </div>
                <input
                  data-testid="input-editFormProjectlist-warehouse"
                  type="text"
                  name="warehouse"
                  onChange={handleChange}
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* Fifth row - Transaction Classification*/}
          <div className="grid grid-cols-12 gap-x-1 gap-y-0">
            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-customerNo">
                    {t("project.customerNo")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input-editFormProjectlist-customerNo"
                    type="text"
                    name="customerNo"
                    onChange={handleChange}
                    className="border-black px-2"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-xs min-w-[140px] px-1 py-2 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-transactionClass">
                    {t("project.transClass")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input1-editFormProjectlist-transactionClass"
                    type="text"
                    name="transactionCode"
                    onChange={handleChange}
                    className="border-r border-black w-[40%] text-center"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-transactionClass"
                    type="text"
                    name="transactionName"
                    onChange={handleChange}
                    className="w-[60%] pl-2"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-documentType">
                    {t("project.docsType")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input-editFormProjectlist-documentType"
                    type="text"
                    name="documentType"
                    onChange={handleChange}
                    className="border-black px-2"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-xs min-w-[130px] text-center whitespace-break-spaces">
                  <span data-testid="text-editFormProjectlist-processCompletion">
                    {t("project.status")}
                  </span>
                </div>
                <div className="flex">
                  <input
                    data-testid="input1-editFormProjectlist-processCompletion"
                    type="text"
                    onChange={handleChange}
                    className="border-r border-black w-[40%]"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-processCompletion"
                    type="text"
                    onChange={handleChange}
                    className="w-[60%]"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Sixth row - Site name */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-start-1 col-span-6">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white text-sm min-w-[140px] px-1 py-2 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-siteName">
                    {t("project.siteName")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input-editFormProjectlist-siteName"
                    type="text"
                    name="siteName"
                    onChange={handleChange}
                    className="w-[100%]"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[140px] p-1.5 text-center whitespace-nowrap">
                  <span data-testid="text-editFormProjectlist-recipientContact">
                    {t("project.recipientContact")}
                  </span>
                </div>
                <div className="flex w-full">
                  <input
                    data-testid="input-editFormProjectlist-recipientContact"
                    type="text"
                    name="recipientContact"
                    onChange={handleChange}
                    className="border-black px-2"
                  />
                </div>
              </div>
            </div>

            <div className="col-span-3">
              <div className="flex border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white min-w-[130px] text-center p-0.5 text-xs whitespace-break-spaces">
                  <span data-testid="text-editFormProjectlist-processingDate">
                    {t("project.cmpdate")}
                  </span>
                </div>
                <div className="relative w-full">
                  <DatePicker
                    data-testid="datepicker-editFormProjectlist-processingDate"
                    ref={processingCompletionDateRef}
                    selected={formData.ProcessingCompletionDate}
                    onChange={(date) =>
                      handleDateChange(date, "ProcessingCompletionDate")
                    }
                    popperPlacement="bottom-start"
                    portalId="root-portal"
                    inline={false}
                    dateFormat="yyyy/MM/dd"
                    className="flex-1 w-full h-full border-none outline-none px-2 pt-1 pr-9"
                    isClearable={formData.ProcessingCompletionDate !== null}
                  />
                  {!formData.ProcessingCompletionDate && (
                    <AiTwotoneCalendar
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                      size={18}
                      onClick={() =>
                        handleOpenDatePicker(processingCompletionDateRef)
                      }
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Seventh row - Process Status */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-12">
              <div className="flex w-full h-10 border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white px-2 flex items-center justify-center min-w-[140px]">
                  <span data-testid="text-editFormProjectlist-process">
                    {t("project.process")}
                  </span>
                </div>
                {/* Process 1-9 */}
                {[
                  { num: "1", name: t("project.Shirring") },
                  { num: "2", name: t("project.Processing") },
                  { num: "3", name: t("project.Vendor") },
                  { num: "4", name: t("project.Welding") },
                  { num: "5", name: t("project.Finishing") },
                  { num: "6", name: t("project.Color") },
                  { num: "7", name: t("project.Rust Prevention") },
                  { num: "8", name: t("project.Vibration") },
                  { num: "9", name: t("project.Completion") },
                ].map((process) => (
                  <React.Fragment key={process.num}>
                    <div className="flex items-center justify-center px-2 border-r border-black">
                      <input
                        data-testid={`checkbox-editFormProjectlist-${process.num}`}
                        type="checkbox"
                        name="processStatus"
                        value={process.num}
                        checked={formData.processStatus.includes(process.num)}
                        onChange={(e) => {
                          const value = e.target.value;
                          setFormData((prev) => ({
                            ...prev,
                            processStatus: e.target.checked
                              ? [...prev.processStatus, value]
                              : prev.processStatus.filter(
                                  (item) => item !== value
                                ),
                          }));
                        }}
                        className="size-4 rounded-lg"
                      />
                    </div>
                    <div className="bg-gray-200 flex items-center justify-center w-[10%] border-r border-black text-xs">
                      <span className="mr-2">{process.num}</span>
                      <span
                        data-testid={`text-editFormProjectlist-${process.num}`}
                      >
                        {process.name}
                      </span>
                    </div>
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>

          {/* Eighth row - Material */}
          <div className="grid grid-cols-12 gap-1">
            <div className="col-span-8">
              <div className="flex w-full border border-black rounded-sm overflow-hidden">
                <div className="bg-[#4472C4] text-white px-2 py-10 flex items-center justify-center min-w-[140px]">
                  <span data-testid="text-editFormProjectlist-material">
                    {t("project.material")}
                  </span>
                </div>
                <div className="flex flex-col w-full">
                  <input
                    data-testid="input1-editFormProjectlist-material"
                    type="text"
                    name="material1"
                    onChange={handleChange}
                    className="border-b border-black px-2 py-1"
                  />
                  <input
                    data-testid="input2-editFormProjectlist-material"
                    type="text"
                    name="material2"
                    onChange={handleChange}
                    className="border-b border-black px-2 py-1"
                  />
                  <input
                    data-testid="input3-editFormProjectlist-material"
                    type="text"
                    name="material3"
                    onChange={handleChange}
                    className="border-b border-black px-2 py-1"
                  />
                  <input
                    data-testid="input4-editFormProjectlist-material"
                    type="text"
                    name="material4"
                    onChange={handleChange}
                    className="border-b border-black px-2 py-1"
                  />
                  <input
                    data-testid="input5-editFormProjectlist-material"
                    type="text"
                    name="material5"
                    onChange={handleChange}
                    className="border-b border-black px-2 py-1"
                  />
                  <input
                    data-testid="input6-editFormProjectlist-material"
                    type="text"
                    name="material6"
                    onChange={handleChange}
                    className="px-2 py-1"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Product Information Grid */}
          <div className="my-5">
            <div className="border border-black rounded-sm overflow-x-auto">
              <div
                className="grid"
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(14, minmax(80px, 1fr))",
                }}
              >
                {/* Row 1 - Main Headers */}
                <div
                  className="bg-[#4472C4] text-white border-r border-black row-span-4 text-sm flex items-center justify-center text-center"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-b  border-black row-span-3 flex items-center justify-center text-center text-sm"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-classification">
                    {t("project.classification")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-productCode">
                    {t("project.prodCode")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-retailPrce">
                    {t("project.retailPrce")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <span data-testid="text-editFormProjectlist-remarks">
                    {t("project.remarks")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 3" }}
                >
                  <span data-testid="text-editFormProjectlist-deliveryDate2">
                    {t("project.specDeliDate")}
                  </span>
                </div>

                {/* Row 2 - Sub Headers */}
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black py-1 flex items-center justify-center text-center row-span-2 text-sm"
                  style={{ gridColumn: "span 3" }}
                >
                  <span data-testid="text-editFormProjectlist-productName">
                    {t("project.pdName")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-tax">
                    {t("project.tax")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderQuantity">
                    {t("project.odQty")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderPrice">
                    {t("project.odPrce")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black py-1 flex items-center justify-center text-center row-span-2 text-sm"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-unitPriceUpdate">
                    {t("project.unitUp")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderAmount">
                    {t("project.odAmount")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-[11px] flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderTaxAmount">
                    {t("project.odTaxAmount")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 3" }}
                >
                  <span data-testid="text-editFormProjectlist-shipDate2">
                    {t("project.schShipDate")}
                  </span>
                </div>

                {/* Row 3 - Third Level Headers */}
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-unit">
                    {t("project.unit")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-allocatedQuantity">
                    {t("project.allocatedQty")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-costUnitPrice">
                    {t("project.csUnitPrce")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-costAmount">
                    {t("project.csAmount")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-orderNo2">
                    {t("project.order")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-soldOut">
                    {t("project.soldOut")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-shipmentComplete">
                    {t("project.shipmentComplete")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-stickyNote">
                    {t("project.stickyNote")}
                  </span>
                </div>

                {/* Row 4 - Fourth Level Headers */}
                <div
                  className="bg-[#4472C4] text-white border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <span data-testid="text-editFormProjectlist-arrangementCode">
                    {t("project.arrangementCode")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <span data-testid="text-editFormProjectlist-supplier">
                    {t("project.supplier")}
                  </span>
                </div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                ></div>
                <div
                  className="bg-[#4472C4] text-white border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 7" }}
                ></div>

                {/* Product Information Grid Form */}
                {/* Row 1 - Main Headers */}
                <div
                  className="bg-gray-100 border-r border-black row-span-4 text-sm flex items-center justify-center text-center"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black row-span-3 flex items-center justify-center text-center text-sm"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-classification"
                    name="classification"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-productCode"
                    name="productCode"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-retailPrice"
                    name="retailPrice"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-remarks"
                    name="remarks"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="border-r border-t border-black text-sm flex items-center text-end py-1"
                  style={{ gridColumn: "span 3" }}
                >
                  <div className="relative w-full">
                    <DatePicker
                      data-testid="datepicker-editFormProjectlist-deliveryDate2"
                      ref={specifiedDeliveryDate2Ref}
                      selected={formData.specifiedDeliveryDate2}
                      onChange={(date) =>
                        handleDateChange(date, "specifiedDeliveryDate2")
                      }
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      inline={false}
                      dateFormat="yyyy/MM/dd"
                      className="flex-1 w-full h-full border-none outline-none px-2 pt-1 pr-9"
                      isClearable={formData.specifiedDeliveryDate2 !== null}
                    />
                    {!formData.specifiedDeliveryDate2 && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                        size={16}
                        onClick={() =>
                          handleOpenDatePicker(specifiedDeliveryDate2Ref)
                        }
                      />
                    )}
                  </div>
                </div>

                {/* Row 2 - Sub Headers */}
                <div
                  className="bg-gray-100 border-r border-t border-black py-1 flex items-center justify-center text-center row-span-2 text-sm"
                  style={{ gridColumn: "span 3" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-productName"
                    name="productName"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-tax"
                    name="tax"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderQuantity"
                    name="orderQuantity"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderPrice"
                    name="orderPrice"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black py-1 flex items-center justify-center text-center row-span-2 text-sm"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-unitPriceUpdate"
                    name="unitPriceUpdate"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderAmount"
                    name="orderAmount"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderTaxAmount"
                    name="orderTaxAmount"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="border-r border-t border-black text-sm flex items-center text-end py-1"
                  style={{ gridColumn: "span 3" }}
                >
                  <div className="relative w-full">
                    <DatePicker
                      data-testid="datepicker-editFormProjectlist-shipDate2"
                      ref={scheduledShipDate2Ref}
                      selected={formData.scheduledShipDate2}
                      onChange={(date) =>
                        handleDateChange(date, "scheduledShipDate2")
                      }
                      popperPlacement="bottom-start"
                      portalId="root-portal"
                      inline={false}
                      dateFormat="yyyy/MM/dd"
                      className="flex-1 w-full h-full border-none outline-none px-2 pt-1 pr-9"
                      isClearable={formData.scheduledShipDate2 !== null}
                    />
                    {!formData.scheduledShipDate2 && (
                      <AiTwotoneCalendar
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                        size={16}
                        onClick={() =>
                          handleOpenDatePicker(scheduledShipDate2Ref)
                        }
                      />
                    )}
                  </div>
                </div>

                {/* Row 3 - Third Level Headers */}
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-unit"
                    name="unit"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-allocatedQuantity"
                    name="allocatedQuantity"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-costUnitPrice"
                    name="costUnitPrice"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-costAmount"
                    name="costAmount"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-orderNo2"
                    name="orderNo2"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-soldOut"
                    name="soldOut"
                    type="checkbox"
                    className="size-4 rounded-lg"
                    title="Sold out"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-shipmentComplete"
                    name="shipmentComplete"
                    type="checkbox"
                    className="size-4 rounded-lg"
                    title="Shipment complete"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-stickyNote"
                    name="stickyNote"
                    type="checkbox"
                    className="size-4 rounded-lg"
                    title="Shipment complete"
                  />
                </div>

                {/* Row 4 - Fourth Level Headers */}
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-arrangementCode"
                    name="arrangementCode"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-supplier"
                    name="supplier"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 7" }}
                >
                  <input
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>

                {/* Row 5 - Fifth Level Footer */}
                <div
                  className="bg-[#4472C4] border-r border-t border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 1" }}
                >
                  <span className="text-white text-sm">
                    <span data-testid="text-editFormProjectlist-remarks2">
                      {t("project.remarks")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 4" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-remarks2"
                    name="remarks2"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-[#4472C4] border-r border-t border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 2" }}
                >
                  <span className="text-white text-sm">
                    <span data-testid="text-editFormProjectlist-AmountTaxTotal">
                      {t("project.total")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input1-editFormProjectlist-AmountTaxTotal"
                    name="amount"
                    type="number"
                    defaultValue={0}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input2-editFormProjectlist-AmountTaxTotal"
                    name="tax"
                    type="number"
                    defaultValue={0}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input3-editFormProjectlist-AmountTaxTotal"
                    name="total"
                    type="number"
                    defaultValue={0}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-[#4472C4] border-r border-t border-b border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 1" }}
                >
                  <span className="text-white text-xs">
                    <span data-testid="text-editFormProjectlist-dateEntryPerson">
                      {t("project.dataEntryPerson")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-b border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input1-editFormProjectlist-dataEntryPerson"
                    name="dataEntryPerson"
                    type="text"
                    value={formData.dataEntryPerson}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-b border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 2" }}
                >
                  <input
                    data-testid="input2-editFormProjectlist-dataEntryDate"
                    name="dataEntryDate"
                    type="text"
                    value={formData.dataEntryDate}
                    onChange={handleChange}
                    className="w-full h-full text-center text-sm"
                  />
                </div>

                {/* Row 6 - Sixth Level Footer */}
                <div
                  className="bg-[#4472C4] border-r border-t border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 1" }}
                >
                  <span className="text-white text-sm">
                    <span data-testid="text-editFormProjectlist-deliveryCourse">
                      {t("project.deliveryCourse")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 4" }}
                >
                  <input
                    data-testid="input-editFormProjectlist-deliveryCourse"
                    name="deliveryCourse"
                    type="text"
                    className="w-full h-full text-center text-sm"
                  />
                </div>
                <div
                  className="bg-[#4472C4] border-r border-t border-black text-sm flex items-center justify-center text-center py-2"
                  style={{ gridColumn: "span 2" }}
                >
                  <span className="text-white text-xs">
                    <span data-testid="text-editFormProjectlist-CostGrossProfitGrossProfitRate">
                      {t("project.totalCost")}
                    </span>
                  </span>
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input1-editFormProjectlist-CostGrossProfitGrossProfitRate"
                    name="cost"
                    type="number"
                    defaultValue={0}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <input
                    data-testid="input2-editFormProjectlist-CostGrossProfitGrossProfitRate"
                    name="grossProfit"
                    type="number"
                    defaultValue={0}
                    className="w-full h-full text-end text-xs"
                  />
                </div>
                <div
                  className="bg-gray-100 border-r border-t border-black text-sm flex items-center justify-center text-center py-1"
                  style={{ gridColumn: "span 1" }}
                >
                  <div className="relative w-full h-full">
                    <input
                      data-testid="input3-editFormProjectlist-CostGrossProfitGrossProfitRate"
                      name="grossProfitRate"
                      type="number"
                      value={formData.grossProfitRate.toFixed(2)}
                      onChange={handleChange}
                      className="w-full h-full text-end text-xs"
                    />
                    <span
                      data-testid="text-editFormProjectlist-percent"
                      className="bg-gray-200 absolute -right-5 top-4 -translate-y-1/2 border border-black p-1 text-xs text-black"
                    >
                      %
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EditProjectlist;
