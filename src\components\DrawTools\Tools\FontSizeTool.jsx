import { useState, useEffect } from "react";
import { useUndo } from "../../../contexts/UndoContext";

// React-icons
import { FaMinus, FaPlus } from "react-icons/fa";

function FontSizeTool({ activeTool, stageRef }) {
  const [fontSize, setFontSize] = useState(18);
  const [inputValue, setInputValue] = useState("18");
  const { boxes, setBoxes } = useUndo();

  // Update input value when fontSize changes
  useEffect(() => {
    setInputValue(fontSize.toString());
  }, [fontSize]);

  if (activeTool !== "textHeight") return null;

  const handleIncreaseFontSize = () => {
    setFontSize((prev) => Math.min(prev + 2, 72));
    updateAllTextFontSizes(Math.min(fontSize + 2, 72));
  };

  const handleDecreaseFontSize = () => {
    setFontSize((prev) => Math.max(prev - 2, 10));
    updateAllTextFontSizes(Math.max(fontSize - 2, 10));
  };

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  const handleInputBlur = () => {
    const newSize = parseInt(inputValue, 10);
    if (!isNaN(newSize)) {
      const validSize = Math.min(Math.max(newSize, 10), 72);
      setFontSize(validSize);
      updateAllTextFontSizes(validSize);
    } else {
      setInputValue(fontSize.toString());
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.target.blur();
    }
  };

  const updateAllTextFontSizes = (newSize) => {
    if (stageRef && stageRef.current) {
      const stage = stageRef.current;
      const layer = stage.findOne("Layer");

      if (!layer) return;

      // Update the boxes state for text boxes
      setBoxes((prev) =>
        prev.map((box) => ({
          ...box,
          fontSize: newSize,
        }))
      );

      // Update text boxes
      const textGroups = layer.find(".text-box-group");
      textGroups.forEach((group) => {
        const text = group.findOne("Text");
        if (text) {
          text.fontSize(newSize);
        }
      });

      // Update dimension text
      const dimensionGroups = layer.find(".dimension-group");
      dimensionGroups.forEach((group) => {
        const text = group.findOne("Text");
        if (text) {
          text.fontSize(newSize);
        }
      });

      // Update standalone text elements
      const textNodes = layer.find("Text");
      textNodes.forEach((text) => {
        // Skip text that are part of groups we already handled
        const parent = text.getParent();
        if (parent && (parent.hasName("text-box-group") || parent.hasName("dimension-group"))) {
          return;
        }
        text.fontSize(newSize);
      });

      layer.draw();
    }
  };

  return (
    <div className="absolute right-0 top-36 z-20 bg-white rounded-lg shadow-md p-2 flex items-center space-x-2">
      <button
        onClick={handleDecreaseFontSize}
        className="p-1 hover:bg-gray-100 rounded"
      >
        <FaMinus className="text-gray-700" />
      </button>
      <input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        onKeyDown={handleKeyDown}
        className="w-12 text-center text-gray-800 font-medium border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      <button
        onClick={handleIncreaseFontSize}
        className="p-1 hover:bg-gray-100 rounded"
      >
        <FaPlus className="text-gray-700" />
      </button>
    </div>
  );
}

export default FontSizeTool;
