import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import en from "./locales/en/en.json";
import jp from "./locales/jp/jp.json";

i18n.use(initReactI18next).init({
  resources: {
    en: { translation: en },
    jp: { translation: jp },
  },
  lng: "en", // ภาษาเริ่มต้น
  fallbackLng: "en", // ถ้า key ไม่เจอในภาษาปัจจุบัน
  interpolation: { escapeValue: false },
});

export default i18n;
