import { useState, useEffect } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

function EditRank() {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const { t } = useTranslation();
  const { rankToEdit } = location.state || {};

  const [rankForm, setRankForm] = useState({
    rank: "",
    sellingPrice: "",
    rate: "",
    curvedObject: "",
    cuttingBoard: "",
  });

  // Load data when component mounts
  useEffect(() => {
    if (rankToEdit) {
      setRankForm({
        rank: rankToEdit.rank || "",
        sellingPrice: rankToEdit.sellingPrice || "",
        rate: rankToEdit.rate || "",
        curvedObject: rankToEdit.curvedObject || "",
        cuttingBoard: rankToEdit.cuttingBoard || "",
      });
    }
  }, [rankToEdit]);

  const handleChange = (field, value) => {
    setRankForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = () => {
    console.log("Updating rank with id:", id);
    console.log("Updated data:", rankForm);
    navigate("/rank");
  };

  const handleCancel = () => {
    navigate("/rank");
  };

  return (
    <div className="max-w-5xl mx-auto p-3">
      <h1
        data-testid="text-editFormRank-rank"
        className="text-2xl font-bold mb-6"
      >
        {t("createAndEditRank.rankEdit")}
      </h1>

      <div className="mb-4">
        <div className="flex flex-row items-center gap-2">
          <div className="flex flex-col flex-[1] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.rank")}
            </label>
            {/* Rank input */}
            <input
              data-testid="input-editFormRank-rank"
              name="rank"
              type="text"
              value={rankForm.rank}
              onChange={(e) => handleChange("rank", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3 text-center"
            />
          </div>

          {/* Selling Price input */}
          <div className="flex flex-col flex-[3] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.sellingPrice")}
            </label>
            <input
              data-testid="input-editFormRank-sellingPrice"
              name="sellingPrice"
              type="number"
              value={rankForm.sellingPrice}
              onChange={(e) => handleChange("sellingPrice", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3"
            />
          </div>

          {/* Rate input */}
          <div className="flex flex-col flex-[2] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.ratePercent")}
            </label>
            <input
              data-testid="input-editFormRank-rate"
              name="rate"
              type="number"
              value={rankForm.rate}
              onChange={(e) => handleChange("rate", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3"
            />
          </div>

          {/* curvedObject input */}
          <div className="flex flex-col flex-[2] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.curvedObject")}
            </label>
            <input
              data-testid="input-editFormRank-curvedObject"
              name="curvedObject"
              type="number"
              value={rankForm.curvedObject}
              onChange={(e) => handleChange("curvedObject", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3"
            />
          </div>

          {/* Cutting Board input */}
          <div className="flex flex-col flex-[2] min-w-0">
            <label className="text-sm font-medium text-gray-700 mb-1">
              {t("createAndEditRank.cuttingBoard")}
            </label>
            <input
              data-testid="input-editFormRank-cuttingBoard"
              name="cuttingBoard"
              type="number"
              value={rankForm.cuttingBoard}
              onChange={(e) => handleChange("cuttingBoard", e.target.value)}
              className="h-10 border border-gray-500 rounded-md px-3"
            />
          </div>

          {/* Required indicator */}
          <div className="pt-6">
            <span className="text-xl font-bold">*</span>
          </div>
        </div>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          data-testid="button-editFormRank-saveRank"
          onClick={handleSave}
        />
        <CancelButton
          data-testid="button-editFormRank-cancelRank"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default EditRank;
