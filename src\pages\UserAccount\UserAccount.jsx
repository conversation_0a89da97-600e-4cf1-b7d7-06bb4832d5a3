import { useEffect, useState } from "react";
import Editbutton from "../../components/EditButton";
import Deletebutton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

const data = [
  { column1: "A001", column2: "Emi", column3: "1234" },
  { column1: "A002", column2: "Ai", column3: "1234" },
  { column1: "A003", column2: "<PERSON>", column3: "1234" },
  { column1: "A004", column2: "<PERSON>", column3: "1234" },
  { column1: "A005", column2: "<PERSON>", column3: "1234" },
  { column1: "A006", column2: "<PERSON>", column3: "1234" },
  { column1: "A007", column2: "<PERSON>", column3: "1234" },
  { column1: "A008", column2: "<PERSON>", column3: "1234" },
  { column1: "A009", column2: "<PERSON>", column3: "1234" },
  { column1: "A010", column2: "<PERSON>", column3: "1234" },
  { column1: "A011", column2: "<PERSON>", column3: "1234" },
  { column1: "A012", column2: "Eva", column3: "1234" },
  { column1: "A013", column2: "Ethan", column3: "1234" },
  { column1: "A014", column2: "Sophia", column3: "1234" },
  { column1: "A001", column2: "Emi", column3: "1234" },
];

function UserAccount() {
  const [currentPage, setCurrentPage] = useState(0);
  const [searchInput, setSearchInput] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();
  const { t } = useTranslation();
  const handleCreate = () => {
    navigate("/usermanagement");
  };

  const handleedit = () => {
    navigate("/useredit");
  };

  // Clear Search input
  useEffect(() => {
    if (searchInput.trim() === "") {
      setSearchTerm("");
      setCurrentPage(0);
    }
  }, [searchInput]);

  // Handle Search Input
  const handleSearchClick = () => {
    setSearchTerm(searchInput);
    setCurrentPage(0);
  };

  const filteredData = data.filter(
    (item) =>
      item.column1.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.column2.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const paginatedData = filteredData.slice(
    currentPage * 10,
    (currentPage + 1) * 10
  );

  const totalPages = Math.ceil(filteredData.length / 10);

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1
          data-testid="text-useraccount"
          name="textUseraccount"
          className="text-2xl font-bold sm:mb-0 lg:ml-20 md:lg:ml-20 sm:ml-20"
        >
          {t("userAccount.userAccountList")}
        </h1>
        <button
          data-testid="button-createForm-user"
          name="buttonCreateFormuser"
          className="btn-create"
          onClick={handleCreate}
        >
          {t("action.create")}
        </button>
      </div>

      <div className="mt-1 w-full flex gap-4">
        <input
          data-testid="input-user-search"
          name="inputUsersearch"
          type="text"
          placeholder={t("userAccount.searchPlace")}
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          className="w-6/12 p-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />

        <button
          data-testid="button-user-search"
          name="buttonUsersearch"
          onClick={handleSearchClick}
          className="btn-search"
        >
          {t("userAccount.search")}
        </button>
      </div>

      <div className="mt-2 w-full overflow-x-auto">
        <table
          data-testid="table-user"
          name="table-user"
          className="w-full bg-white border-collapse"
        >
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[50px]">
                {t("userAccount.userID")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[200px]">
                {t("userAccount.username")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[200px]">
                {t("userAccount.password")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[100px]">
                {t("userAccount.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((item, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-white px-4 py-1">
                  {item.column1}
                </td>
                <td className="border border-white px-4 py-1">
                  {item.column2}
                </td>
                <td className="border border-white px-4 py-1">
                  {item.column3}
                </td>
                <td className="border border-white px-4 py-1">
                  <div className="flex justify-center space-x-2">
                    <Editbutton
                      dataTestId="button-editForm-user"
                      name="buttonEditFormuser"
                      onClick={handleedit}
                    />
                    <Deletebutton
                      dataTestId="button-user-delete"
                      name="buttonUserdelete"
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedData.length === 0 && (
              <tr>
                <td colSpan="3" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Footer
        className="fixed table-footer-group"
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredData.length}
      />
    </div>
  );
}

export default UserAccount;
