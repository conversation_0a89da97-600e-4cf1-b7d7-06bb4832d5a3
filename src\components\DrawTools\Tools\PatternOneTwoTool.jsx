import { v4 as uuidv4 } from "uuid";

// Keep original default dimensions
export const DEFAULT_HORIZONTAL_LENGTH = 80;
export const MAX_HORIZONTAL_LENGTH = 200;
export const DEFAULT_VERTICAL_LENGTH = 40;
export const DEFAULT_STEP_HEIGHT = 20;
export const DEFAULT_VERTICAL_OFFSET = 150;
const CIRCLE_RADIUS = 25;
const INLINE_CIRCLE_RADIUS = 20;

// Add these constants for the text labels
const BOTTOM_LEFT_INNER_LABEL = "-3";
const TOP_RIGHT_INNER_LABEL = "-3";
const BOTTOM_LEFT_LABEL = "15";
const BOTTOM_LEFT_RIGHT_LABEL = "97°";
const TOP_RIGHT_LABEL = "16.8";
const TOP_RIGHT_LEFT_LABEL = "97°";
const SLOPE_TEXT_LABEL = "166";

export function createPatternOneTwo(dimensions, stageRef) {
  // Get stage dimensions for centering
  const stage = stageRef.current;
  const centerX = stage.width() / 2;
  const centerY = stage.height() / 2;

  // Scale factor to make the pattern larger
  const scale = 3;

  // Starting point (bottom-left of the pattern)
  const startX = centerX - 40 * scale;
  // Move the pattern down by increasing the Y coordinate with the vertical offset
  const startY = centerY + 60 * scale + DEFAULT_VERTICAL_OFFSET;

  // Fixed offset for the right vertical dimension
  const rightDimensionOffset = 80;

  // Minimum safe distance for the horizontal dimension
  const MIN_SAFE_DISTANCE = 50;

  // Calculate points for the pattern based on the image
  const points = [
    startX,
    startY,
    startX,
    startY - DEFAULT_VERTICAL_LENGTH * scale,
    startX + DEFAULT_HORIZONTAL_LENGTH * scale,
    startY - 60 * scale,
    startX + DEFAULT_HORIZONTAL_LENGTH * scale,
    startY - 60 * scale - DEFAULT_STEP_HEIGHT * scale,
  ];

  // Calculate the highest point of the pattern
  const highestPointY = startY - 60 * scale - DEFAULT_STEP_HEIGHT * scale;

  // Position the horizontal dimension above the highest point with a safe margin
  const horizontalDimensionY = highestPointY - MIN_SAFE_DISTANCE;

  // Calculate positions for the annotation circles
  // Bottom left corner circle
  const bottomLeftCircleX = startX;
  const bottomLeftCircleY = startY;

  // Top right corner circle
  const topRightCircleX = startX + DEFAULT_HORIZONTAL_LENGTH * scale;
  const topRightCircleY = startY - 60 * scale - DEFAULT_STEP_HEIGHT * scale;

  // Bottom left inner circle (at the first vertical segment)
  const bottomLeftInnerCircleX = startX - 20;
  const bottomLeftInnerCircleY = startY - DEFAULT_VERTICAL_LENGTH * scale - 30;

  // Top right inner circle (at the top of the right vertical segment)
  const topRightInnerCircleX = startX + DEFAULT_HORIZONTAL_LENGTH * scale + 20;
  const topRightInnerCircleY = startY - 60 * scale + 30;

  // Create the pattern elements
  return [
    {
      id: uuidv4(),
      type: "polyline",
      points: points,
      stroke: "black",
      strokeWidth: 2,
      // Add reference to which dimensions control this shape
      linkedDimensions: ["horizontal", "top", "middle", "bottom"],
    },
    // Horizontal dimension (top)
    {
      id: uuidv4(),
      type: "dimension",
      from: [startX, horizontalDimensionY],
      to: [startX + DEFAULT_HORIZONTAL_LENGTH * scale, horizontalDimensionY],
      text: DEFAULT_HORIZONTAL_LENGTH,
      isVertical: false,
      isEditable: true,
      dimensionId: "horizontal",
      textPosition: "top",
    },
    // Left vertical dimensions
    {
      id: uuidv4(),
      type: "dimension",
      from: [
        startX - 100,
        startY - 40 * scale - DEFAULT_STEP_HEIGHT * 2 * scale,
      ],
      to: [startX - 100, startY - 40 * scale - DEFAULT_STEP_HEIGHT * scale],
      text: DEFAULT_STEP_HEIGHT,
      isVertical: true,
      isEditable: true,
      textOffset: 40,
      dimensionId: "top",
    },
    {
      id: uuidv4(),
      type: "dimension",
      from: [startX - 100, startY - 40 * scale - DEFAULT_STEP_HEIGHT * scale],
      to: [startX - 100, startY - 40 * scale],
      text: DEFAULT_STEP_HEIGHT,
      isVertical: true,
      isEditable: true,
      textOffset: 40,
      dimensionId: "middle",
    },
    {
      id: uuidv4(),
      type: "dimension",
      from: [startX - 100, startY - 40 * scale],
      to: [startX - 100, startY],
      text: 40,
      isVertical: true,
      isEditable: true,
      textOffset: 40,
      dimensionId: "bottom",
    },
    // Right vertical dimension
    {
      id: uuidv4(),
      type: "dimension",
      from: [
        startX + DEFAULT_HORIZONTAL_LENGTH * scale + rightDimensionOffset,
        startY - 40 * scale - DEFAULT_STEP_HEIGHT * 2 * scale,
      ],
      to: [
        startX + DEFAULT_HORIZONTAL_LENGTH * scale + rightDimensionOffset,
        startY - 40 * scale - DEFAULT_STEP_HEIGHT * scale,
      ],
      text: "ない",
      isVertical: true,
      isEditable: false,
      textOffset: 40,
      dimensionId: "nai",
    },
    // Add text label along the slope
    {
      id: uuidv4(),
      type: "text_label",
      x: startX + (DEFAULT_HORIZONTAL_LENGTH * scale) / 2,
      y: startY - 60 * scale,
      text: SLOPE_TEXT_LABEL,
      fontSize: 16,
      fill: "black",
      isEditable: false,
      textOffset: 40,
      isVisible: false,
    },
    // Bottom left corner circle
    {
      id: uuidv4(),
      type: "circle",
      x: bottomLeftCircleX,
      y: bottomLeftCircleY,
      radius: CIRCLE_RADIUS,
      stroke: "#07a4fe",
      strokeWidth: 1,
      fill: "transparent",
      cornerPosition: "bottomLeft",
      isCircleVisible: false,
      BF_LeftLebel: BOTTOM_LEFT_LABEL,
      BF_RightLabel: BOTTOM_LEFT_RIGHT_LABEL,
    },
    // Top right corner circle
    {
      id: uuidv4(),
      type: "circle",
      x: topRightCircleX,
      y: topRightCircleY,
      radius: CIRCLE_RADIUS,
      stroke: "#07a4fe",
      strokeWidth: 1,
      fill: "transparent",
      cornerPosition: "topRight",
      isCircleVisible: false,
      TR_RightLabel: TOP_RIGHT_LABEL,
      TR_LeftLabel: TOP_RIGHT_LEFT_LABEL,
    },
    // Bottom left inner circle (at the first vertical segment)
    {
      id: uuidv4(),
      type: "circle",
      x: bottomLeftInnerCircleX,
      y: bottomLeftInnerCircleY,
      radius: INLINE_CIRCLE_RADIUS,
      stroke: "#07a4fe",
      strokeWidth: 1,
      fill: "transparent",
      cornerPosition: "bottomLeftInner",
      isCircleVisible: false,
      bottomInnerLabel: BOTTOM_LEFT_INNER_LABEL,
    },
    // Top right inner circle (at the top of the right vertical segment)
    {
      id: uuidv4(),
      type: "circle",
      x: topRightInnerCircleX,
      y: topRightInnerCircleY,
      radius: INLINE_CIRCLE_RADIUS,
      stroke: "#07a4fe",
      strokeWidth: 1,
      fill: "transparent",
      cornerPosition: "topRightInner",
      isCircleVisible: false,
      topInnerLabel: TOP_RIGHT_INNER_LABEL,
    },
  ];
}

// Add a new function to update the pattern when dimensions change
export function updatePatternOneTwo(shapes, dimensionId, newValue) {
  const changedDimension = shapes.find(
    (shape) => shape.type === "dimension" && shape.id === dimensionId
  );

  if (!changedDimension) return shapes;

  // Find the polyline shape that needs to be updated
  const polylineIndex = shapes.findIndex(
    (shape) =>
      shape.type === "polyline" &&
      shape.linkedDimensions?.includes(changedDimension.dimensionId)
  );

  if (polylineIndex === -1) return shapes;

  // Get the current polyline and its points
  const polyline = shapes[polylineIndex];
  const points = [...polyline.points];

  // Get the scale factor from the original implementation
  const scale = 3;

  // Find all vertical dimensions to track their values
  const verticalDimensions = shapes.filter(
    (shape) => shape.type === "dimension" && shape.isVertical
  );

  // Get the dimensions by their dimensionId
  const topDimension = verticalDimensions.find(
    (dim) => dim.dimensionId === "top"
  );
  const middleDimension = verticalDimensions.find(
    (dim) => dim.dimensionId === "middle"
  );
  const bottomDimension = verticalDimensions.find(
    (dim) => dim.dimensionId === "bottom"
  );

  // Calculate the minimum safe distance for the horizontal dimension
  const MIN_SAFE_DISTANCE = 50;

  // Update points based on which dimension changed
  if (changedDimension.dimensionId === "horizontal") {
    // Limit the horizontal length to MAX_HORIZONTAL_LENGTH
    const limitedValue = Math.min(newValue, MAX_HORIZONTAL_LENGTH);

    // Update the horizontal length
    const startX = points[0];
    points[4] = startX + limitedValue * scale;
    points[6] = startX + limitedValue * scale;

    // Update the dimension text to show the limited value
    changedDimension.text = limitedValue;
  } else if (changedDimension.dimensionId === "top") {
    // Update the top height
    const topHeight = newValue * scale;
    points[7] = points[5] - topHeight;
  } else if (changedDimension.dimensionId === "middle") {
    // Update the middle section height
    const middleHeight = newValue * scale;

    // Store the current bottom position
    const bottomY = points[1];

    // Store the current bottom section height
    const bottomHeight = bottomDimension
      ? bottomDimension.text * scale
      : points[3] - points[1];

    // Update points for the middle section
    points[3] = bottomY - bottomHeight;
    points[5] = points[3] - middleHeight;

    // Update the top position
    if (topDimension) {
      const topHeight = topDimension.text * scale;
      points[7] = points[5] - topHeight;
    }
  } else if (changedDimension.dimensionId === "bottom") {
    // Update the bottom section height
    const bottomHeight = newValue * scale;

    // Store the current bottom position
    const bottomY = points[1];

    // Update bottom section
    points[3] = bottomY - bottomHeight;

    // Get current heights for other sections
    const middleHeight = middleDimension
      ? middleDimension.text * scale
      : points[5] - points[3];
    const topHeight = topDimension
      ? topDimension.text * scale
      : points[7] - points[5];

    // Update middle and top sections
    points[5] = points[3] - middleHeight;
    points[7] = points[5] - topHeight;
  }

  // Create updated shapes array with the modified points
  let updatedShapes = shapes.map((shape, index) => {
    if (index === polylineIndex) {
      return {
        ...shape,
        points: points,
      };
    } else if (shape.type === "text_label") {
      const startSlopeX = points[2];
      const startSlopeY = points[3];
      const endSlopeX = points[4];
      const endSlopeY = points[5];

      // Calculate the midpoint of the sloped line
      const midX = startSlopeX + (endSlopeX - startSlopeX) * 0.5;
      const midY = startSlopeY + (endSlopeY - startSlopeY) * 0.6;

      // Add a small offset perpendicular to the slope for better visibility
      const angle = Math.atan2(
        endSlopeY - startSlopeY,
        endSlopeX - startSlopeX
      );
      const offset = 20;

      return {
        ...shape,
        x: midX + offset * Math.sin(angle),
        y: midY - offset * Math.cos(angle),
      };
    } else if (shape.type === "circle") {
      // Update circle positions based on their corner position
      if (shape.cornerPosition === "bottomLeft") {
        return {
          ...shape,
          x: points[0],
          y: points[1],
        };
      } else if (shape.cornerPosition === "topRight") {
        return {
          ...shape,
          x: points[6],
          y: points[7],
        };
      } else if (shape.cornerPosition === "bottomLeftInner") {
        return {
          ...shape,
          x: points[0] - 20,
          y: points[3] - 30,
        };
      } else if (shape.cornerPosition === "topRightInner") {
        return {
          ...shape,
          x: points[4] + 20,
          y: points[5] + 30,
        };
      }
      return shape;
    } else if (shape.type === "dimension") {
      // Update the dimension that was changed
      if (shape.id === dimensionId) {
        // If this is the horizontal dimension that was changed
        if (
          changedDimension.dimensionId === "horizontal" &&
          !shape.isVertical
        ) {
          // Limit the horizontal length to MAX_HORIZONTAL_LENGTH
          const limitedValue = Math.min(newValue, MAX_HORIZONTAL_LENGTH);
          const startX = points[0];
          const endX = startX + limitedValue * scale;
          return {
            ...shape,
            from: [startX, shape.from[1]],
            to: [endX, shape.to[1]],
            text: newValue,
          };
        }
        // If this is the top dimension that was changed
        else if (changedDimension.dimensionId === "top") {
          return {
            ...shape,
            from: [shape.from[0], points[5]],
            to: [shape.to[0], points[7]],
            text: newValue,
          };
        }
        // If this is the middle dimension that was changed
        else if (changedDimension.dimensionId === "middle") {
          return {
            ...shape,
            from: [shape.from[0], points[3]],
            to: [shape.to[0], points[5]],
            text: newValue,
          };
        }
        // If this is the bottom dimension that was changed
        else if (changedDimension.dimensionId === "bottom") {
          return {
            ...shape,
            from: [shape.from[0], points[1]],
            to: [shape.to[0], points[3]],
            text: newValue,
          };
        }
        // For any other dimension just update the text
        return {
          ...shape,
          text: newValue,
        };
      }

      // Update all horizontal dimensions when any dimension changes
      if (!shape.isVertical) {
        const startX = points[0];
        const currentHorizontalLength =
          shapes.find(
            (s) => s.type === "dimension" && s.dimensionId === "horizontal"
          )?.text || DEFAULT_HORIZONTAL_LENGTH;
        const endX =
          startX +
          Math.min(currentHorizontalLength, MAX_HORIZONTAL_LENGTH) * scale;

        // Calculate the new y-position for the horizontal dimension
        // It should be above the highest point of the pattern with a safe margin
        const highestPointY = points[7];
        const newHorizontalY = highestPointY - MIN_SAFE_DISTANCE;

        return {
          ...shape,
          from: [startX, newHorizontalY],
          to: [endX, newHorizontalY],
          textPosition: "top",
        };
      }

      // Update all vertical dimensions when any vertical dimension changes
      if (
        (changedDimension.dimensionId === "top" ||
          changedDimension.dimensionId === "middle" ||
          changedDimension.dimensionId === "bottom") &&
        shape.isVertical &&
        shape.id !== changedDimension.id
      ) {
        // Update top dimension
        if (shape.dimensionId === "top") {
          return {
            ...shape,
            from: [shape.from[0], points[5]],
            to: [shape.to[0], points[7]],
          };
        }

        // Update middle dimension
        if (shape.dimensionId === "middle") {
          return {
            ...shape,
            from: [shape.from[0], points[3]],
            to: [shape.to[0], points[5]],
          };
        }

        // Update bottom dimension
        if (shape.dimensionId === "bottom") {
          return {
            ...shape,
            from: [shape.from[0], points[1]],
            to: [shape.to[0], points[3]],
          };
        }

        // Always update the ない dimension to match the top dimension
        if (shape.dimensionId === "nai" || shape.text === "ない") {
          // Get the current top dimension height and position
          const topDim = verticalDimensions.find(
            (dim) => dim.dimensionId === "top"
          );
          if (topDim) {
            const currentHorizontalLength =
              shapes.find(
                (s) => s.type === "dimension" && s.dimensionId === "horizontal"
              )?.text || DEFAULT_HORIZONTAL_LENGTH;
            const limitedHorizontalLength = Math.min(
              currentHorizontalLength,
              MAX_HORIZONTAL_LENGTH
            );

            const rightEdgeX = points[0] + limitedHorizontalLength * scale;
            const fixedOffset = 80;

            return {
              ...shape,
              from: [rightEdgeX + fixedOffset, points[5]],
              to: [rightEdgeX + fixedOffset, points[7]],
            };
          }
        }
      }

      // Special case: Always update the ない dimension when top dimension changes
      if (
        changedDimension.dimensionId === "top" &&
        (shape.dimensionId === "nai" || shape.text === "ない")
      ) {
        const currentHorizontalLength =
          shapes.find(
            (s) => s.type === "dimension" && s.dimensionId === "horizontal"
          )?.text || DEFAULT_HORIZONTAL_LENGTH;
        const limitedHorizontalLength = Math.min(
          currentHorizontalLength,
          MAX_HORIZONTAL_LENGTH
        );

        const rightEdgeX = points[0] + limitedHorizontalLength * scale;
        const fixedOffset = 80;

        return {
          ...shape,
          from: [rightEdgeX + fixedOffset, points[5]],
          to: [rightEdgeX + fixedOffset, points[7]],
        };
      }

      // Update the right vertical dimension (with "ない" text) when horizontal length changes
      if (
        changedDimension.dimensionId === "horizontal" &&
        (shape.dimensionId === "nai" || shape.text === "ない")
      ) {
        // Use the limitedValue from the horizontal dimension update
        const limitedHorizontalValue = Math.min(
          newValue,
          MAX_HORIZONTAL_LENGTH
        );
        const rightEdgeX = points[0] + limitedHorizontalValue * scale;
        const fixedOffset = 80; // Keep consistent spacing from the right edge

        return {
          ...shape,
          from: [rightEdgeX + fixedOffset, points[5]],
          to: [rightEdgeX + fixedOffset, points[7]],
        };
      }
    }
    return shape;
  });

  // Get the stageRef from the first shape that has it
  const stageRefShape = shapes.find((shape) => shape.stageRef);
  const stageRef = stageRefShape?.stageRef;

  // Center the pattern after updating dimensions
  if (stageRef && stageRef.current) {
    updatedShapes = centerPatternOneTwo(updatedShapes, stageRef);
  }

  return updatedShapes;
}

export function centerPatternOneTwo(shapes, stageRef) {
  if (!stageRef || !stageRef.current) return shapes;

  const stage = stageRef.current;
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Find the polyline shape
  const polylineIndex = shapes.findIndex((shape) => shape.type === "polyline");
  if (polylineIndex === -1) return shapes;

  const polyline = shapes[polylineIndex];
  const points = [...polyline.points];

  // Calculate current pattern bounds
  let minX = Infinity,
    maxX = -Infinity,
    minY = Infinity,
    maxY = -Infinity;
  for (let i = 0; i < points.length; i += 2) {
    minX = Math.min(minX, points[i]);
    maxX = Math.max(maxX, points[i]);
    minY = Math.min(minY, points[i + 1]);
    maxY = Math.max(maxY, points[i + 1]);
  }

  // Calculate pattern center and dimensions
  const patternCenterX = (minX + maxX) / 2;
  const patternCenterY = (minY + maxY) / 2;

  // Calculate the stage center
  const stageCenterX = stageWidth / 2;
  const stageCenterY = stageHeight / 2;

  // Calculate the offset to center the pattern horizontally only
  // For vertical positioning, add the vertical offset
  const offsetX = stageCenterX - patternCenterX;
  const offsetY = stageCenterY - patternCenterY + DEFAULT_VERTICAL_OFFSET / 2;

  return shapes.map((shape) => {
    if (shape.type === "polyline") {
      const newPoints = [...shape.points];
      for (let i = 0; i < newPoints.length; i += 2) {
        newPoints[i] += offsetX;
        newPoints[i + 1] += offsetY;
      }
      return { ...shape, points: newPoints };
    } else if (shape.type === "dimension") {
      return {
        ...shape,
        from: [shape.from[0] + offsetX, shape.from[1] + offsetY],
        to: [shape.to[0] + offsetX, shape.to[1] + offsetY],
      };
    } else if (shape.type === "circle") {
      return {
        ...shape,
        x: shape.x + offsetX,
        y: shape.y + offsetY,
      };
    } else if (shape.type === "text_label") {
      return {
        ...shape,
        x: shape.x + offsetX,
        y: shape.y + offsetY,
      };
    }
    return shape;
  });
}
