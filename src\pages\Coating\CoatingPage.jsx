import { useState } from "react";
import { useNavigate } from "react-router-dom";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";

const mockCoating = [
  {
    id: 1,
    paintType: "Paint 1",
    colorType: "Color 1",
    cosmeticSurface: "Cosmetic Surface 1",
    priceClassification: "Price Classification 1",
    priceByMaterial: "Price By Material 1",
  },
  {
    id: 2,
    paintType: "Paint 2",
    colorType: "Color 2",
    cosmeticSurface: "Cosmetic Surface 2",
    priceClassification: "Price Classification 2",
    priceByMaterial: "Price By Material 2",
  },
  {
    id: 3,
    paintType: "Paint 3",
    colorType: "Color 3",
    cosmeticSurface: "Cosmetic Surface 3",
    priceClassification: "Price Classification 3",
    priceByMaterial: "Price By Material 3",
  },
  {
    id: 4,
    paintType: "Paint 4",
    colorType: "Color 4",
    cosmeticSurface: "Cosmetic Surface 4",
    priceClassification: "Price Classification 4",
    priceByMaterial: "Price By Material 4",
  },
  {
    id: 5,
    paintType: "Paint 5",
    colorType: "Color 5",
    cosmeticSurface: "Cosmetic Surface 5",
    priceClassification: "Price Classification 5",
    priceByMaterial: "Price By Material 5",
  },
];

function CoatingPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(0);
  const [filteredCoating, setFilteredCoating] = useState(mockCoating);

  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredCoating.length / itemsPerPage);
  const paginatedCoating = filteredCoating.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateCoating = () => {
    navigate("/create-coating");
  };

  const handleEditCoating = (id) => {
    const coatingToEdit = mockCoating.find((item) => item.id === id);
    navigate(`/edit-coating/${id}`, { state: { coatingToEdit } });
  };

  const handleDeleteClick = (id) => {
    console.log("Delete coating with id:", id);
  };

  return (
    <div className="w-full">
      <div className="flex items-center mb-3 justify-between">
        <h1
          data-testid="text-coating"
          name="textCoating"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("coating.coating")}
        </h1>
        <button
          data-testid="button-createForm-coating"
          name="createCoating"
          type="button"
          onClick={handleCreateCoating}
          className="btn-create"
        >
          {t("action.create") || "Create"}
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full bg-white border-collapse">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="py-2 px-4 text-left border border-white min-w-[50px]">
                {t("coating.No")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.paintType")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.colorType")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.cosmeticSurface")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.priceClassification")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("coating.priceByMaterial")}
              </th>
              <th className="py-2 px-4 text-center border border-white min-w-[120px]">
                {t("coating.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedCoating.map((coating, index) => (
              <tr
                key={coating.id}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="py-2 px-4 border border-white">{coating.id}</td>
                <td className="py-2 px-4 border border-white">
                  {coating.paintType}
                </td>
                <td className="py-2 px-4 border border-white">
                  {coating.colorType}
                </td>
                <td className="py-2 px-4 border border-white">
                  {coating.cosmeticSurface}
                </td>
                <td className="py-2 px-4 border border-white">
                  {coating.priceClassification}
                </td>
                <td className="py-2 px-4 border border-white">
                  {coating.priceByMaterial}
                </td>
                <td className="py-2 px-4 border border-white">
                  <div className="flex justify-center gap-2">
                    <EditButton
                      onClick={() => handleEditCoating(coating.id)}
                      dataTestId="button-editForm-coating"
                    />
                    <DeleteButton
                      dataTestId="button-delete-coating"
                      onClick={() => handleDeleteClick(coating.id)}
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedCoating.length === 0 && (
              <tr>
                <td colSpan="6" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredCoating.length}
      />
    </div>
  );
}

export default CoatingPage;
