import { useState, useEffect, useRef } from "react";

// React-icons
import { RiDeleteBack2Fill } from "react-icons/ri";

function NumericKeypad({ initialValue = "", onConfirm, onCancel }) {
  // Initialize with empty string instead of initialValue
  const [value, setValue] = useState("");
  const confirmButtonRef = useRef(null);

  // Add non-passive event listeners for touch events
  useEffect(() => {
    const confirmButton = confirmButtonRef.current;

    if (confirmButton) {
      const handleTouchStart = (e) => {
        e.preventDefault();
        e.stopPropagation();
      };

      const handleTouchEnd = (e) => {
        e.preventDefault();
        e.stopPropagation();
        // Convert to number if possible
        const numericValue = !isNaN(parseFloat(value))
          ? parseFloat(value)
          : value;
        // Call onConfirm directly with the numeric value
        onConfirm(numericValue);
      };

      // Add event listeners with { passive: false } option
      confirmButton.addEventListener("touchstart", handleTouchStart, {
        passive: false,
      });
      confirmButton.addEventListener("touchend", handleTouchEnd, {
        passive: false,
      });

      // Clean up
      return () => {
        confirmButton.removeEventListener("touchstart", handleTouchStart);
        confirmButton.removeEventListener("touchend", handleTouchEnd);
      };
    }
  }, [value, onConfirm]); // Add dependencies to ensure latest value is used

  const handleNumberClick = (num) => {
    setValue((prev) => prev + num);
  };

  const handleClear = () => {
    setValue("");
  };

  const handleBackspace = () => {
    setValue((prev) => prev.slice(0, -1));
  };

  const handleConfirm = (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Convert to number if possible before passing to onConfirm
    const numericValue = !isNaN(parseFloat(value)) ? parseFloat(value) : value;
    onConfirm(numericValue);
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30"
      onClick={(e) => e.stopPropagation()}
    >
      <div
        className="bg-white rounded-lg shadow-xl w-64"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Display */}
        <div className="flex p-2 border-b">
          <input
            type="text"
            className="flex p-2 text-right text-xl border rounded w-full"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.stopPropagation();
                handleConfirm();
              }
            }}
            onClick={(e) => e.stopPropagation()}
          />
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleBackspace();
            }}
            className="ml-2 px-4 bg-green-500 text-white rounded"
          >
            <RiDeleteBack2Fill className="w-8 h-8" />
          </button>
        </div>

        {/* Keypad */}
        <div className="grid grid-cols-4">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleNumberClick("1");
            }}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            1
          </button>
          <button
            onClick={() => handleNumberClick("2")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            2
          </button>
          <button
            onClick={() => handleNumberClick("3")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            3
          </button>
          <button
            ref={confirmButtonRef}
            onClick={(e) => {
              e.stopPropagation();
              handleConfirm(e);
            }}
            className="p-4 border bg-green-500 text-white hover:bg-green-600 row-span-1"
          >
            ✓
          </button>

          <button
            onClick={() => handleNumberClick("4")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            4
          </button>
          <button
            onClick={() => handleNumberClick("5")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            5
          </button>
          <button
            onClick={() => handleNumberClick("6")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            6
          </button>
          <button
            onClick={() => handleNumberClick(".")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            .
          </button>

          <button
            onClick={() => handleNumberClick("7")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            7
          </button>
          <button
            onClick={() => handleNumberClick("8")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            8
          </button>
          <button
            onClick={() => handleNumberClick("9")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            9
          </button>
          <button
            onClick={() => handleNumberClick("0")}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600"
          >
            0
          </button>

          <button
            onClick={handleClear}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600 col-span-2"
          >
            Clear
          </button>
          <button
            onClick={onCancel}
            className="p-4 border bg-gray-500 text-white hover:bg-gray-600 col-span-2"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}

export default NumericKeypad;