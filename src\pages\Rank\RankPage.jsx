import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";

function RankPage() {
  const [currentPage, setCurrentPage] = useState(0);
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Mock data for ranks
  const mockRanks = [
    {
      id: 1,
      rank: "A",
      sellingPrice: 5,
      rate: 8,
      magmono: 100,
      cuttingBoard: 100,
    },
    {
      id: 2,
      rank: "C",
      sellingPrice: 40,
      rate: 45,
      magmono: 150,
      cuttingBoard: 150,
    },
    {
      id: 3,
      rank: "B",
      sellingPrice: 20,
      rate: 23,
      magmono: 150,
      cuttingBoard: 150,
    },
    {
      id: 4,
      rank: "D",
      sellingPrice: 12,
      rate: 15,
      magmono: 150,
      cuttingBoard: 150,
    },
    {
      id: 5,
      rank: "E",
      sellingPrice: 1,
      rate: 0,
      magmono: 100,
      cuttingBoard: 500,
    },
  ];

  const [filteredRanks, setFilteredRanks] = useState(mockRanks);

  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredRanks.length / itemsPerPage);
  const paginatedRanks = filteredRanks.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateClick = () => {
    navigate("/create-rank");
  };

  const handleEditClick = (id) => {
    const rankToEdit = mockRanks.find((item) => item.id === id);
    navigate(`/edit-rank/${id}`, { state: { rankToEdit } });
  };

  const handleDeleteClick = (id) => {
    console.log("Delete rank with id:", id);
  };

  return (
    <div className="w-full">
      {/* Header Section */}
      <div className="flex items-center mb-3 justify-between">
        <h1
          data-testid="text-rank"
          name="textRank"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("rank.rankManagement")}
        </h1>
        <button
          data-testid="button-createForm-rank"
          name="createRank"
          type="button"
          onClick={handleCreateClick}
          className="btn-create"
        >
          {t("action.create") || "Create"}
        </button>
      </div>

      {/* Table Section */}
      <div className="relative overflow-x-auto shadow-md">
        <table
          data-testid="table-rank"
          className="w-full bg-white border-collapse"
        >
          <thead>
            <tr className="bg-[#4472C4] text-white">
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.rank") || "Rank"}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.sellingPrice") || "Selling price"}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.ratePercent") || "Rate (%)"}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.curvedObject") || "Curved Object"}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("rank.cuttingBoard") || "Cutting board"}
              </th>
              <th className="py-2 px-4 text-center border border-white">
                {t("rank.operation") || "operation"}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedRanks.map((rank, index) => (
              <tr
                key={rank.id}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="py-2 px-4 border border-white">{rank.rank}</td>
                <td className="py-2 px-4 border border-white">
                  {rank.sellingPrice}
                </td>
                <td className="py-2 px-4 border border-white">{rank.rate}</td>
                <td className="py-2 px-4 border border-white">
                  {rank.magmono}
                </td>
                <td className="py-2 px-4 border border-white">
                  {rank.cuttingBoard}
                </td>
                <td className="py-2 px-4 border border-white">
                  <div className="flex justify-center gap-2">
                    <EditButton onClick={() => handleEditClick(rank.id)} />
                    <DeleteButton onClick={() => handleDeleteClick(rank.id)} />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedRanks.length === 0 && (
              <tr>
                <td colSpan="6" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredRanks.length}
      />
    </div>
  );
}

export default RankPage;
