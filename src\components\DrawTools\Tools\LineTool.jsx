import { useEffect, useState } from "react";
import <PERSON>n<PERSON> from "konva";
import { useUndo } from "../../../contexts/UndoContext";
import { v4 as uuidv4 } from "uuid";

function LineTool({ stageRef, activeTool }) {
  const { addAction, lines, setLines } = useUndo();
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentLine, setCurrentLine] = useState(null);

  const SNAP_THRESHOLD = 10;

  // Handle line drawing based on active tool
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    const lineTools = ["solid", "dashed", "dotDash", "dotDash2"];
    if (!lineTools.includes(activeTool)) {
      stage.container().style.cursor = "default";
      stage.off("mousedown touchstart");
      stage.off("mousemove touchmove");
      stage.off("mouseup touchend");
      return;
    }

    stage.container().style.cursor = "crosshair";

    const handleMouseDown = (e) => {
      e.evt.preventDefault();

      const pos = stage.getPointerPosition();
      setIsDrawing(true);

      // Create new line
      const newLine = {
        id: uuidv4(),
        tool: activeTool,
        points: [pos.x, pos.y, pos.x, pos.y],
      };

      setCurrentLine(newLine);
    };

    const handleMouseMove = (e) => {
      if (!isDrawing) return;
      e.evt.preventDefault();

      const pos = stage.getPointerPosition();
      const [x1, y1] = currentLine.points;

      let { x, y } = pos;

      // Snap to grid
      if (Math.abs(x - x1) < SNAP_THRESHOLD) {
        x = x1;
      } else if (Math.abs(y - y1) < SNAP_THRESHOLD) {
        y = y1;
      }

      setCurrentLine((prev) => ({
        ...prev,
        points: [x1, y1, x, y],
      }));
    };

    const handleMouseUp = () => {
      if (!isDrawing || !currentLine) return;
      setIsDrawing(false);

      const [x1, y1, x2, y2] = currentLine.points;
      const dx = x2 - x1;
      const dy = y2 - y1;
      const length = Math.sqrt(dx * dx + dy * dy);

      if (length < 20) {
        setCurrentLine(null);
        return;
      }

      setLines((prev) => [...prev, currentLine]);
      addAction({ type: "add-line", payload: currentLine, shapeType: "line" });

      setCurrentLine(null);
    };

    // Add event listeners
    stage.on("mousedown touchstart", handleMouseDown);
    stage.on("mousemove touchmove", handleMouseMove);
    stage.on("mouseup touchend", handleMouseUp);

    // Cleanup
    return () => {
      stage.off("mousedown touchstart");
      stage.off("mousemove touchmove");
      stage.off("mouseup touchend");
    };
  }, [activeTool, isDrawing, currentLine]);

  // Draw lines on the stage
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    // Clear only lines that aren't in our state array
    const lineShapes = layer.find(".line-shape");
    lineShapes.forEach((line) => {
      const lineId = line.id().replace("line-", "");
      // Only destroy lines that aren't in our state array
      if (!lines.some((l) => l.id.toString() === lineId)) {
        line.destroy();
      }
    });

    // Import Konva dynamically
    if (!Konva) return;

    // Draw all saved lines
    lines.forEach((line) => {
      // Check if this line already exists on the stage
      const existingLine = layer.findOne(`#line-${line.id}`);
      if (existingLine) {
        // Update the points to match the current state
        existingLine.points(line.points);
        return;
      }

      const lineConfig = getLineConfig(line.tool);

      const lineShape = new Konva.Line({
        points: line.points,
        stroke: "black",
        strokeWidth: 2,
        lineCap: "round",
        lineJoin: "round",
        name: "line-shape",
        id: `line-${line.id}`,
        hitStrokeWidth: 10,
        ...lineConfig,
      });

      layer.add(lineShape);
    });

    // Draw current line if in drawing mode
    if (isDrawing && currentLine) {
      const lineConfig = getLineConfig(currentLine.tool);

      const lineShape = new Konva.Line({
        points: currentLine.points,
        stroke: "black",
        strokeWidth: 2,
        lineCap: "round",
        lineJoin: "round",
        name: "line-shape",
        id: "current-line",
        ...lineConfig,
      });

      layer.add(lineShape);
    }

    layer.draw();
  }, [lines, isDrawing, currentLine]);

  // Helper function to get line configuration based on tool type
  const getLineConfig = (toolType) => {
    switch (toolType) {
      case "dashed":
        return { dash: [6, 3] };
      case "dotDash":
        return { dash: [10, 3, 3, 3] };
      case "dotDash2":
        return { dash: [10, 3, 3, 3, 3, 3] };
      case "solid":
      default:
        return {};
    }
  };

  return null;
}

export default LineTool;
