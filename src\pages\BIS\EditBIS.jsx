import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

// import components
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

function EditBIS() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { bisToEdit } = location.state || {};

  const [formData, setFormData] = useState({
    screwType: "",
    color: "",
    price: "",
  });

  useEffect(() => {
    if (bisToEdit) {
      setFormData(bisToEdit);
    }
  }, [bisToEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = () => {
    console.log("Updated form data:", formData);
    navigate("/bis");
  };

  const handleCancel = () => {
    navigate("/bis");
  };

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1
          data-testid="text-createFormMaterial-material"
          name="textcreateFormMaterialmaterial"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("bis.bisAdd")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton
            dataTestId="button-createFormMaterial-save"
            name="buttoncreateFormMaterialsave"
          />
          <Canclebutton
            dataTestId="button-createFormMaterial-cancle"
            name="buttoncreateFormMaterialcancle"
            onClick={handleCancel}
          />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-name"
                name="textcreateFormMaterialname"
                className="block font-bold mb-1"
              >
                {t("bis.screwType")}
              </label>
              <input
                data-testid="input-createFormMaterial-name"
                name="inputcreateFormMaterialname"
                type="text"
                value={formData.screwType}
                onChange={handleChange}
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-code"
                name="textcreateFormMaterialcode"
                className="block font-bold mb-1"
              >
                {t("bis.price")}
              </label>
              <input
                data-testid="input-createFormMaterial-code"
                name="inputcreateFormMaterialcode"
                type="number"
                value={formData.price}
                onChange={handleChange}
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="w-full">
            <label
              data-testid="text-createFormMaterial-length"
              name="textcreateFormMateriallength"
              className="block font-bold mb-1"
            >
              {t("bis.color")}
            </label>
            <input
              data-testid="input-createFormMaterial-length"
              name="inputcreateFormMateriallength"
              type="text"
              value={formData.color}
              onChange={handleChange}
              className="w-full md:w-96 border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
            />
          </div>
        </div>
      </div>

      <div className="overflow-x-auto mt-5">
        <table
          data-testid="table-createFormMaterial"
          name="tablecreateFormMaterial"
          className="w-full bg-white border-collapse text-sm"
        >
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.screwType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.color")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("bis.price")}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-[#E9EDF9]">
              <td className="border border-gray-300 px-4 py-1">
                {formData.screwType}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.color}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.price}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default EditBIS;
