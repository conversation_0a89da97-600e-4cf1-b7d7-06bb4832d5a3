import { createContext, useContext, useState } from "react";

const FullscreenContext = createContext();

export function FullscreenProvider({ children }) {
  const [isFullscreenActive, setIsFullscreenActive] = useState(false);

  return (
    <FullscreenContext.Provider value={{ isFullscreenActive, setIsFullscreenActive }}>
      {children}
    </FullscreenContext.Provider>
  );
}

export function useFullscreen() {
  return useContext(FullscreenContext);
}