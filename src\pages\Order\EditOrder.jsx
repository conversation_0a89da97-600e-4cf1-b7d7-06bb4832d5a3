import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

// React-icons
import { AiTwotoneCalendar } from "react-icons/ai";

function createOrder() {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { orderToEdit } = location.state || {};

  const orderDateRef = useRef(null);
  const specifiedDeliveryDateRef = useRef(null);

  const [formData, setFormData] = useState(
    orderToEdit || {
      orderDate: null,
      orderNo: "",
      specifiedDeliveryDate: null,
      customerAbbr: "",
      siteName: "",
      contactName: "",
      deliveryDate: "",
      recipientContact: "",
      deliveryRoute: "",
      deliveryDestinationAbbreviation: "",
      rank: "",
      pi: "",
    }
  );

  useEffect(() => {
    if (orderToEdit) {
      setFormData(orderToEdit);
    }
  }, [orderToEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleOpenDatePicker = (ref) => {
    if (ref.current) {
      ref.current.setOpen(true);
    }
  };

  const handleSave = () => {
    console.log("Updated form data:", formData);
    // Add save logic here
  };

  const handleCancel = () => {
    navigate("/import-orders");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-editFormOrder-order"
        className="text-2xl font-bold mb-4"
      >
        {t("createOrder.orderEdit")}
      </h1>

      <div className="grid grid-cols-2 gap-2.5">
        {/* First Row */}
        <div>
          <label
            data-testid="text-editFormOrder-orderDate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.orderDate")}
          </label>
          <div className="relative w-full">
            <DatePicker
              data-testid="datepicker-editFormOrder-orderDate"
              ref={orderDateRef}
              wrapperClassName="w-full"
              selected={formData.orderDate}
              onChange={(date) => setFormData({ ...formData, orderDate: date })}
              popperPlacement="bottom-start"
              portalId="root-portal"
              inline={false}
              dateFormat="dd/MM/yyyy"
              isClearable={formData.orderDate !== null}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {!formData.orderDate && (
              <AiTwotoneCalendar
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                size={18}
                onClick={() => handleOpenDatePicker(orderDateRef)}
              />
            )}
          </div>
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-orderNo"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.orderNo")}
          </label>
          <input
            data-testid="input-editFormOrder-orderNo"
            type="text"
            name="orderNo"
            value={formData.orderNo}
            readOnly
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 bg-gray-100"
          />
        </div>

        {/* Second Row */}
        <div>
          <label
            data-testid="text-editFormOrder-specifiedDeliveryDate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.specifiedDeliveryDate")}
          </label>
          <div className="relative w-full">
            <DatePicker
              data-testid="datepicker-editFormOrder-specifiedDeliveryDate"
              ref={specifiedDeliveryDateRef}
              wrapperClassName="w-full"
              selected={formData.specifiedDeliveryDate}
              onChange={(date) =>
                setFormData({ ...formData, specifiedDeliveryDate: date })
              }
              popperPlacement="bottom-start"
              portalId="root-portal"
              inline={false}
              dateFormat="dd/MM/yyyy"
              isClearable={formData.specifiedDeliveryDate !== null}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {!formData.specifiedDeliveryDate && (
              <AiTwotoneCalendar
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                size={18}
                onClick={() => handleOpenDatePicker(specifiedDeliveryDateRef)}
              />
            )}
          </div>
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-customerAbbr"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.customerAbb")}
          </label>
          <input
            data-testid="input-editFormOrder-customerAbbr"
            type="text"
            name="customerAbbr"
            value={formData.customerAbbr}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-siteName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.siteName")}
          </label>
          <input
            data-testid="input-editFormOrder-siteName"
            type="text"
            name="siteName"
            value={formData.siteName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-contactName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.contactName")}
          </label>
          <input
            data-testid="input-editFormOrder-contactName"
            type="text"
            name="contactName"
            value={formData.contactName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-recipientContact"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.recipientContact")}
          </label>
          <input
            data-testid="input-editFormOrder-recipientContact"
            type="text"
            name="recipientContact"
            value={formData.recipientContact}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-deliveryRoute"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.deliveryRoute")}
          </label>
          <input
            data-testid="input-editFormOrder-deliveryRoute"
            type="text"
            name="deliveryRoute"
            value={formData.deliveryRoute}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-deliveryDestinationAbbreviation"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.deliveryDestinationAbbreviation")}
          </label>
          <input
            data-testid="input-editFormOrder-deliveryDestinationAbbreviation"
            type="text"
            name="deliveryDestinationAbbreviation"
            value={formData.deliveryDestinationAbbreviation}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-rank"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.rank")}
          </label>
          <input
            data-testid="input-editFormOrder-rank"
            type="text"
            name="rank"
            value={formData.rank}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormOrder-pi"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.pi")}
          </label>
          <input
            data-testid="input-editFormOrder-pi"
            type="number"
            name="pi"
            value={formData.pi}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-editFormOrder-save"
          onClick={handleSave}
        />
        <CancelButton
          dataTestId="button-editFormOrder-cancel"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default createOrder;
