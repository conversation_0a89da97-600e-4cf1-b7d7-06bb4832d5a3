import React, { useState } from "react";
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import LanguageSelector from "../components/LanguageSelector";

const LoginPage = () => {
  const [userId, setUserId] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const { login } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSubmit = (e) => {
    e.preventDefault();
    if (userId === "admin" && password === "1234") {
      login(userId); // ← ส่ง userId เข้าไป
      setError(t("loginSuccess"));
      navigate("/home");
    } else {
      setError(t("invalidCredentials"));
    }
  };

  return (
    <div className="relative h-screen bg-[#93c47d]">
      {/* ปุ่มเปลี่ยนภาษา มุมขวาบน */}
      <div className="absolute top-4 right-4">
        <LanguageSelector />
      </div>

      <div className="flex justify-center items-center h-full">
        <form
          onSubmit={handleSubmit}
          className="bg-white p-8 rounded-lg shadow-lg w-[350px]"
        >
          <h2 className="text-3xl font-bold mb-6 text-center text-gray-800">
            {t("sidebar.userAccount")}
          </h2>

          {error && <p className="text-red-500 text-center mb-4">{error}</p>}

          <div className="mb-4">
            <label className="block text-gray-700 font-semibold mb-2">
              {t("common.userid")}
            </label>
            <input
              type="text"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              required
              className="w-full px-4 py-2 border border-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="mb-6">
            <label className="block text-gray-700 font-semibold mb-2">
              {t("common.password")}
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full px-4 py-2 border border-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <button
            type="submit"
            className="w-full bg-blue-900 text-white py-2 rounded-md font-semibold hover:bg-green-700 transition-colors"
          >
            {t("common.login")}
          </button>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
