import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

// import components
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

function EditCoating() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { coatingToEdit } = location.state || {};

  const [formData, setFormData] = useState({
    paintType: "",
    colorType: "",
    cosmeticSurface: "",
    priceClassification: "",
    priceByMaterial: "",
  });

  useEffect(() => {
    if (coatingToEdit) {
      setFormData(coatingToEdit);
    }
  }, [coatingToEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = () => {
    console.log("Updated form data:", formData);
    navigate("/coating");
  };

  const handleCancel = () => {
    navigate("/coating");
  };

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("coating.coatingEdit")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton onClick={handleSave} />
          <Canclebutton onClick={handleCancel} />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.paintType")}
              </label>
              <input
                name="paintType"
                value={formData.paintType}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.colorType")}
              </label>
              <input
                name="colorType"
                value={formData.colorType}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.cosmeticSurface")}
              </label>
              <input
                name="cosmeticSurface"
                value={formData.cosmeticSurface}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.priceClassification")}
              </label>
              <input
                name="priceClassification"
                value={formData.priceClassification}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 3 */}
          <div className="flex flex-col items-start gap-2">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("coating.priceByMaterial")}
              </label>
              <input
                name="priceByMaterial"
                value={formData.priceByMaterial}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.paintType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.colorType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.cosmeticSurface")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.priceClassification")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left w-1/9">
                {t("coating.priceByMaterial")}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-[#E9EDF9]">
              <td className="border border-gray-300 px-4 py-1">
                {formData.paintType}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.colorType}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.cosmeticSurface}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.priceClassification}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.priceByMaterial}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default EditCoating;
