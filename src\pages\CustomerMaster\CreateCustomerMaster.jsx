import { useState } from "react";
import { useNavigate } from "react-router-dom";
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";
import { useTranslation } from "react-i18next";

function CreateCustomerMaster() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    orderNo: "",
    fixedRateAdjustment: "",
    customerAbbr: "",
    siteName: "",
    recipientContact: "",
    deliveryDestination: "",
    shippingClass: "",
    holeDrillingPattern: "",
    processingPattern: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = () => {
    console.log("Form data:", formData);
    // Add save logic here
  };

  const handleCancel = () => {
    navigate("/customer-master");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-customer-create"
        className="text-lg sm:text-xl md:text-2xl font-bold mb-5"
      >
        {t("customer.customerManagement")}
      </h1>

      <div className="grid grid-cols-2 gap-x-6 gap-y-2">
        {/* First Row */}
        <div>
          <label
            data-testid="text-createFormCustomer-orderNo"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.orderNo")}
          </label>
          <input
            data-testid="input-createFormCustomer-orderNo"
            type="text"
            name="orderNo"
            value={formData.orderNo}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormCustomer-fixedRate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.fixedRate")}
          </label>
          <div className="relative w-full">
            <input
              data-testid="input-createFormCustomer-fixedRate"
              type="number"
              name="fixedRateAdjustment"
              value={formData.fixedRateAdjustment}
              onChange={handleChange}
              className="w-full px-4 py-2 pr-10 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <span className="absolute inset-y-0 right-3 flex items-center text-gray-700 text-base pointer-events-none">
              %
            </span>
          </div>
        </div>

        {/* Second Row */}
        <div>
          <label
            data-testid="text-createFormCustomer-customerAbbr"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.customerAbbr")}
          </label>
          <input
            data-testid="input-createFormCustomer-customerAbbr"
            type="text"
            name="customerAbbr"
            value={formData.customerAbbr}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormCustomer-siteName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.siteName")}
          </label>
          <input
            data-testid="input-createFormCustomer-siteName"
            type="text"
            name="siteName"
            value={formData.siteName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Third Row - Full Width */}
        <div>
          <label
            data-testid="text-createFormCustomer-recipientContact"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.recipientContact")}
          </label>
          <input
            data-testid="input-createFormCustomer-recipientContact"
            type="text"
            name="recipientContact"
            value={formData.recipientContact}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div></div>

        {/* Fourth Row */}
        <div>
          <label
            data-testid="text-createFormCustomer-deliveryDestination"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.deliveryDestination")}
          </label>
          <input
            data-testid="input-createFormCustomer-deliveryDestination"
            type="text"
            name="deliveryDestination"
            value={formData.deliveryDestination}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormCustomer-shippingClass"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.shippingClass")}
          </label>
          <select
            data-testid="select-createFormCustomer-shippingClass"
            name="shippingClass"
            value={formData.shippingClass}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="class1">Class 1</option>
            <option value="class2">Class 2</option>
            <option value="class3">Class 3</option>
          </select>
        </div>

        {/* Fifth Row */}
        <div>
          <label
            data-testid="text-createFormCustomer-holeDrillingPattern"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.holeDrillingPattern")}
          </label>
          <select
            data-testid="select-createFormCustomer-holeDrillingPattern"
            name="holeDrillingPattern"
            value={formData.holeDrillingPattern}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="cost1">Cost 1</option>
            <option value="cost2">Cost 2</option>
            <option value="cost3">Cost 3</option>
          </select>
        </div>

        <div>
          <label
            data-testid="text-createFormCustomer-processingPattern"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.processingPattern")}
          </label>
          <select
            data-testid="input-createFormCustomer-processingPattern"
            name="processingPattern"
            value={formData.processingPattern}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="fee1">Fee 1</option>
            <option value="fee2">Fee 2</option>
            <option value="fee3">Fee 3</option>
          </select>
        </div>

        {/* Sixth Row */}
        <div>
          <label
            data-testid="text-createFormCustomer-holePattern"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.holePattern")}
          </label>
          <select
            data-testid="select-createFormCustomer-holePattern"
            name="holePattern"
            value={formData.holePattern}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="pattern1">Pattern 1</option>
            <option value="pattern2">Pattern 2</option>
            <option value="pattern3">Pattern 3</option>
          </select>
        </div>

        <div>
          <label
            data-testid="text-createFormCustomer-rank"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.rank")}
          </label>
          <select
            data-testid="select-createFormCustomer-rank"
            name="rank"
            value={formData.rank}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="rank1">Rank 1</option>
            <option value="rank2">Rank 2</option>
            <option value="rank3">Rank 3</option>
          </select>
        </div>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 mt-8">
        <SaveButton
          dataTestId="button-createFormCustomer-save"
          onClick={handleSave}
        />
        <CancelButton
          dataTestId="button-createFormCustomer-cancel"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default CreateCustomerMaster;
