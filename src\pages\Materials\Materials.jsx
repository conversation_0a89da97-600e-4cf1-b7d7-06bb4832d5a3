import { useState } from "react";
import Editbutton from "../../components/EditButton";
import Deletebutton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

const data = [
  {
    order: "A001",
    materialName: "Aluminum Sheet",
    matetialPrice: "100",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A002",
    materialName: "Steel Rod",
    matetialPrice: "200",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A003",
    materialName: "Copper Wire",
    matetialPrice: "300",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A004",
    materialName: "PVC Pipe",
    matetialPrice: "400",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A005",
    materialName: "Rubber Gasket",
    matetialPrice: "500",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A006",
    materialName: "Glass Panel",
    matetialPrice: "600",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A007",
    materialName: "Plastic Pellets",
    matetialPrice: "700",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A008",
    materialName: "Carbon Fiber",
    matetialPrice: "800",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A009",
    materialName: "Brass Nut",
    matetialPrice: "900",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A010",
    materialName: "Silicone Sealant",
    matetialPrice: "1000",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A011",
    materialName: "Iron Plate",
    matetialPrice: "1100",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A012",
    materialName: "Acrylic Board",
    matetialPrice: "1200",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A013",
    materialName: "Nylon Rope",
    matetialPrice: "1300",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A014",
    materialName: "Foam Insulation",
    matetialPrice: "1400",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
  {
    order: "A001",
    materialName: "Aluminum Sheet",
    matetialPrice: "1500",
    maxLen: "1000",
    thickness: "1.0",
    curObj: "Curved object",
    unitPrc: "100",
  },
];

function Materials() {
  const [currentPage, setCurrentPage] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleCreate = () => {
    navigate("/materialadd");
  };

  const handleEditClick = (id) => {
    const materialToEdit = data.find((item) => item.order === id);
    navigate(`/materialedit/${id}`, { state: { materialToEdit } });
  };

  const filteredData = data.filter(
    (item) =>
      item.order.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.materialName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Start ตัวกรองrow 10 แถว
  const paginatedData = filteredData.slice(
    currentPage * 10,
    (currentPage + 1) * 10
  );

  const totalPages = Math.ceil(filteredData.length / 10);

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("material.materialManagement")}
        </h1>
        <button
          data-testid="button-create-material"
          name="buttonCreateMaterial"
          className="btn-create"
          onClick={handleCreate}
        >
          {t("action.create")}
        </button>
      </div>

      <div className="mt-3 w-full overflow-x-auto">
        <table
          data-testid="table-material"
          name="tableMaterial"
          className="w-full bg-white border-collapse whitespace-nowrap"
        >
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.order")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.materialName")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.maxLen")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.thickness")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.curObj")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.matetialPrice")}
              </th>
              <th className="py-2 px-4 text-left border border-white">
                {t("material.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((item, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.order}
                </td>
                <td className="border border-white px-4 py-2 w-1/2">
                  {item.materialName}
                </td>
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.maxLen}
                </td>
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.thickness}
                </td>
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.curObj}
                </td>
                <td className="border border-white px-4 py-2 w-1/4">
                  {item.matetialPrice}
                </td>
                <td className="border border-white px-4 py-1">
                  <div className="flex justify-center space-x-2">
                    <Editbutton
                      dataTestId="button-edit-material"
                      name="buttonEditMaterial"
                      onClick={() => handleEditClick(item.order)}
                    />
                    <Deletebutton
                      dataTestId="button-delete-material"
                      name="buttonDeleteMaterial"
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedData.length === 0 && (
              <tr>
                <td colSpan="3" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Footer
        className="fixed table-footer-group"
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredData.length}
      />
    </div>
  );
}

export default Materials;
