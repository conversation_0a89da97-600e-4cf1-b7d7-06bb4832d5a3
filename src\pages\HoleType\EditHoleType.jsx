import { useState, useEffect } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

function EditHoleType() {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const { t } = useTranslation();
  // Get the hole type data from location state
  const { holeTypeToEdit } = location.state || {};

  const [holeTypeSets, setHoleTypeSets] = useState([
    {
      holeType: "",
      size: "",
      unit: "",
      materialType: "",
      colorTypes: "",
    },
  ]);

  // Load data when component mounts
  useEffect(() => {
    if (holeTypeToEdit) {
      setHoleTypeSets([
        {
          holeType: holeTypeToEdit.holeType,
          size: holeTypeToEdit.size,
          unit: holeTypeToEdit.unit,
          materialType: holeTypeToEdit.materialType,
          colorTypes: holeTypeToEdit.colorTypes,
        },
      ]);
    }
  }, [holeTypeToEdit]);

  const handleChange = (index, field, value) => {
    const updatedSets = [...holeTypeSets];
    updatedSets[index][field] = value;
    setHoleTypeSets(updatedSets);
  };

  const handleSave = () => {
    console.log("Updating hole type with id:", id);
    console.log("Updated data:", holeTypeSets);
    navigate("/hole-type");
  };

  const handleCancel = () => {
    navigate("/hole-type");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1 data-testid="text-holeType-edit" className="text-2xl font-bold mb-6">
        {t("holeType.hole")}
      </h1>

      {holeTypeSets.map((set, index) => (
        <div
          key={index}
          className="mb-8 p-6 border border-gray-300 rounded-md shadow-sm relative"
        >
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            {/* Hole Type */}
            <div>
              <label
                data-testid="text-editFormHoleType-holeType"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.hole")}
              </label>
              <select
                data-testid="select-editFormHoleType-holeType"
                name="holeType"
                value={set.holeType}
                onChange={(e) =>
                  handleChange(index, "holeType", e.target.value)
                }
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value="">Select hole type</option>
                <option value="Countersink">{t("holeType.counterSink")}</option>
                <option value="Round Hole">{t("holeType.roundHole")}</option>
                <option value="Slot">{t("holeType.slot")}</option>
              </select>
            </div>

            {/* Size */}
            <div>
              <label
                data-testid="text-editFormHoleType-size"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.size")}
              </label>
              <input
                data-testid="input-editFormHoleType-size"
                name="size"
                type="text"
                value={set.size}
                onChange={(e) => handleChange(index, "size", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Unit */}
            <div>
              <label
                data-testid="text-editFormHoleType-unit"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.unit")}
              </label>
              <select
                data-testid="select-editFormHoleType-unit"
                name="unit"
                value={set.unit}
                onChange={(e) => handleChange(index, "unit", e.target.value)}
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value="">Select unit</option>
                <option value="Φ">Φ</option>
              </select>
            </div>

            {/* Material Type */}
            <div>
              <label
                data-testid="text-editFormHoleType-materialType"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.materialType")}
              </label>
              <select
                data-testid="select-editFormHoleType-materialType"
                name="materialType"
                value={set.materialType}
                onChange={(e) =>
                  handleChange(index, "materialType", e.target.value)
                }
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value="">Select material</option>
                <option value="acrylic">Acrylic</option>
                <option value="Urethane">Urethane</option>
                <option value="Fluorine">Fluorine</option>
              </select>
            </div>

            {/* Color Types */}
            <div>
              <label
                data-testid="text-editFormHoleType-colorTypes"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.colorTypes")}
              </label>
              <select
                data-testid="select-editFormHoleType-colorTypes"
                name="colorTypes"
                value={set.colorTypes}
                onChange={(e) =>
                  handleChange(index, "colorTypes", e.target.value)
                }
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value="">Select color</option>
                <option value="Silver">Silver</option>
                <option value="Light color">Light color</option>
                <option value="Dark">Dark</option>
              </select>
            </div>

            {/* Pattern */}
            <div>
              <label
                data-testid="text-editFormHoleType-pattern"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.pattern")}
              </label>
              <input
                data-testid="input-editFormHoleType-pattern"
                name="pattern"
                type="text"
                value={set.pattern}
                onChange={(e) => handleChange(index, "pattern", e.target.value)}
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      ))}

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-editFormHoleType-saveHoleType"
          onClick={handleSave}
        />
        <CancelButton
          dataTestId="button-editFormHoleType-cancelHoleType"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default EditHoleType;
