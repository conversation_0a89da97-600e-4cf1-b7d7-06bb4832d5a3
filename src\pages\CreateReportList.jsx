import { useNavigate, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import { FaArrowLeft, FaPencilAlt, FaFilePdf, FaPrint, FaSave } from "react-icons/fa";
import { FaPlus } from "react-icons/fa";

function CreateReportList() {
  const navigate = useNavigate();
  const location = useLocation();
  const [projectData, setProjectData] = useState({
    projectId: "00201565",
    orderingCompany: "Okayama Building Sash Industry Co., Ltd.",
    contactPerson: "Mr. <PERSON>",
    deliveryDestination: "Same as customer address",
    siteName: "(ML) Yonago Nishinippon Co., Ltd. Factory",
    scheduledShipDate: "2025/01/06",
    shippingClass: "Shipping F",
    material: "aluminum",
    type: "TS-1N",
    thickness: "t - 1.5",
    brand: "Magmono",
    surface: "bis"
  });

  // Initialize with data passed from ReportListPage if available
  useEffect(() => {
    if (location.state?.orderData) {
      const { orderData, projectId } = location.state;
      setProjectData({
        projectId: projectId || "00201565",
        orderingCompany: orderData.orderingCompany,
        contactPerson: orderData.contactPerson,
        deliveryDestination: orderData.deliveryDestination,
        siteName: orderData.siteName,
        scheduledShipDate: orderData.deliveryDate ? 
          orderData.deliveryDate.to