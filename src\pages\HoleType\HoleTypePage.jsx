import { useState } from "react";
import { useNavigate } from "react-router-dom";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";

// Mock data for hole types
const mockHoleTypes = [
  {
    id: 1,
    holeType: "Countersink",
    size: "V4.5D6.4",
    unit: "mm",
    materialType: "acrylic",
    colorTypes: "Silver",
  },
  {
    id: 2,
    holeType: "Round Hole",
    size: "Φ4.5",
    unit: "mm",
    materialType: "acrylic",
    colorTypes: "Silver",
  },
  {
    id: 3,
    holeType: "Slot",
    size: "Φ4.5 x15",
    unit: "mm",
    materialType: "acrylic",
    colorTypes: "Silver",
  },
  {
    id: 4,
    holeType: "Countersink",
    size: "V4.5D7",
    unit: "mm",
    materialType: "Urethane",
    colorTypes: "Light color",
  },
  {
    id: 5,
    holeType: "Round Hole",
    size: "Φ 4.2",
    unit: "mm",
    materialType: "Urethane",
    colorTypes: "Light color",
  },
  {
    id: 6,
    holeType: "Slot",
    size: "Φ4.5 x20",
    unit: "mm",
    materialType: "Urethane",
    colorTypes: "Light color",
  },
  {
    id: 7,
    holeType: "Countersink",
    size: "V4.5D8",
    unit: "mm",
    materialType: "Fluorine",
    colorTypes: "Dark",
  },
  {
    id: 8,
    holeType: "Round Hole",
    size: "Φ3.5",
    unit: "mm",
    materialType: "Fluorine",
    colorTypes: "Dark",
  },
  {
    id: 9,
    holeType: "Slot",
    size: "Φ6 x15",
    unit: "mm",
    materialType: "Fluorine",
    colorTypes: "Dark",
  },
];

function HoleTypePage() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchField, setSearchField] = useState("");
  const [filteredHoleTypes, setFilteredHoleTypes] = useState(mockHoleTypes);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 8;

  // Calculate total pages
  const totalPages = Math.ceil(filteredHoleTypes.length / itemsPerPage);

  // Get current page data
  const paginatedHoleTypes = filteredHoleTypes.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  // Function Search
  const handleSearchClick = () => {
    if (searchField === "") {
      setFilteredHoleTypes(mockHoleTypes);
    } else {
      const filtered = mockHoleTypes.filter(
        (item) => item.holeType === searchField
      );
      setFilteredHoleTypes(filtered);
    }

    setCurrentPage(0);
  };

  const handleCreateClick = () => {
    navigate("/create-holetype");
  };

  const handleEditClick = (id) => {
    // Find the hole type with the matching id
    const holeTypeToEdit = mockHoleTypes.find((item) => item.id === id);

    // Navigate to edit page with the hole type data
    navigate(`/edit-holetype/${id}`, {
      state: { holeTypeToEdit },
    });
  };

  const handleDeleteClick = (id) => {
    console.log("Delete hole type with id:", id);
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-3">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("holeType.hole")}
        </h1>
      </div>

      {/* Header Section */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-3 w-full">
        <div className="flex flex-col sm:flex-row sm:items-center w-full sm:w-auto">
          <div className="flex flex-col sm:flex-row sm:items-center border border-gray-300 rounded-md overflow-hidden sm:mr-3 w-full sm:w-auto">
            <div className="bg-[#4472C4] text-white p-2 min-w-[100px] text-center whitespace-nowrap">
              <span data-testid="text-holeType">{t("holeType.hole")}</span>
            </div>
            <select
              data-testid="select-holeType-search"
              name="selectHoleTypeSearch"
              value={searchField}
              onChange={(e) => setSearchField(e.target.value)}
              className="w-full sm:min-w-[150px] h-10 border-t sm:border-t-0 sm:border-l border-gray-500 rounded-none sm:rounded-r-md shadow-md"
            >
              <option value="">{t("holeType.allType")}</option>
              <option value="Countersink">{t("holeType.counterSink")}</option>
              <option value="Round Hole">{t("holeType.roundHole")}</option>
              <option value="Slot">{t("holeType.slot")}</option>
            </select>
          </div>
        </div>

        <div className="w-full sm:w-auto">
          <button
            data-testid="button-holeType-search"
            onClick={handleSearchClick}
            className="btn-search w-full sm:w-auto"
          >
            {t("holeType.search")}
          </button>
        </div>

        <div className="w-full sm:w-auto md:ml-auto">
          <button
            data-testid="button-createForm-holeType"
            onClick={handleCreateClick}
            className="btn-create w-full sm:w-auto"
          >
            {t("action.create")}
          </button>
        </div>
      </div>

      {/* Table Section */}
      <div className="relative overflow-x-auto shadow-md">
        <table
          data-testid="table-holeType"
          className="w-full bg-white border-collapse"
        >
          <thead>
            <tr className="bg-[#4472C4] text-white">
              <th className="py-3 px-4 text-left border border-white min-w-[50px]">
                NO
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[120px]">
                {t("holeType.hole")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[150px]">
                {t("holeType.size")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[100px]">
                {t("holeType.unit")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[100px]">
                {t("holeType.materialType")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[100px]">
                {t("holeType.colorTypes")}
              </th>
              <th className="py-3 px-4 text-center border border-white min-w-[80px]">
                {t("holeType.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedHoleTypes.map((holeType, index) => (
              <tr
                key={holeType.id}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="py-2 px-4 border border-white">
                  {holeType.id}
                </td>
                <td className="py-2 px-4 border border-white">
                  {holeType.holeType}
                </td>
                <td className="py-2 px-4 border border-white">
                  {holeType.size}
                </td>
                <td className="py-2 px-4 border border-white">
                  {holeType.unit}
                </td>
                <td className="py-2 px-4 border border-white">
                  {holeType.materialType}
                </td>
                <td className="py-2 px-4 border border-white">
                  {holeType.colorTypes}
                </td>
                <td className="py-2 px-4 border border-white">
                  <div className="flex justify-center gap-2">
                    <EditButton
                      dataTestId="button-editForm-holetype"
                      onClick={() => handleEditClick(holeType.id)}
                    />
                    <DeleteButton
                      dataTestId="button-delete-holetype"
                      onClick={() => handleDeleteClick(holeType.id)}
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedHoleTypes.length === 0 && (
              <tr>
                <td colSpan="6" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredHoleTypes.length}
      />
    </div>
  );
}

export default HoleTypePage;
