import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useTranslation } from "react-i18next";
import LanguageSelector from "../components/LanguageSelector";

// React-icons
import {
  FaBars,
  FaUserCog,
  FaUsers,
  FaMedal,
  FaFileImport,
  FaListUl,
  FaCubes,
  FaChartBar,
  FaMoneyCheckAlt,
  FaCircleNotch,
  FaStickyNote,
  FaCommentDots,
  FaPaintBrush,
} from "react-icons/fa";
import { TbDeviceIpad } from "react-icons/tb";

function Sidebar({ isOpen, toggleSidebar }) {
  const location = useLocation();
  const { user, logout } = useAuth();
  const { t } = useTranslation();

  const menuItems = [
    {
      path: "/user-account",
      icon: <FaUserCog />,
      label: t("sidebar.userAccount"),
    },
    {
      path: "/customer-master",
      icon: <FaUsers />,
      label: t("sidebar.customerMaster"),
    },
    { path: "/rank", icon: <FaMedal />, label: t("sidebar.rank") },
    {
      path: "/import-orders",
      icon: <FaFileImport />,
      label: t("sidebar.importOrders"),
    },
    {
      path: "/project-list",
      icon: <FaListUl />,
      label: t("sidebar.projectList"),
    },
    { path: "/material", icon: <FaCubes />, label: t("sidebar.material") },
    { path: "/bis", icon: <FaChartBar />, label: t("sidebar.bis") },
    {
      path: "/processing-fee",
      icon: <FaMoneyCheckAlt />,
      label: t("sidebar.processingFee"),
    },
    {
      path: "/coating",
      icon: <FaPaintBrush />,
      label: t("sidebar.coating"),
    },
    {
      path: "/hole-type",
      icon: <FaCircleNotch />,
      label: t("sidebar.holeType"),
    },
    {
      path: "/remarks-unit-price",
      icon: <FaStickyNote />,
      label: t("sidebar.remarkUnit"),
    },
    {
      path: "/material-remarks",
      icon: <FaCommentDots />,
      label: t("sidebar.remarks"),
    },
    {
      path: "/report-list",
      icon: <TbDeviceIpad />,
      label: "Ipad",
    },
  ];

  return (
    <>
      {/* Header / Navbar */}
      <header className="w-full bg-green-600 text-white fixed top-0 z-50 flex items-center px-4">
        {/* ปุ่ม Sidebar ฝั่งซ้าย */}
        <button
          data-testid="sidebar-toggle-button"
          name="sidebarToggleButton"
          type="button"
          className="text-2xl transition-transform duration-300 ease-in-out"
          onClick={toggleSidebar}
        >
          <FaBars
            className={`text-black w-10 h-10 transform transition-transform duration-300
            ${isOpen ? "rotate-180" : "rotate-0"}`}
          />
        </button>

        {/* กล่องฝั่งขวา: ชื่อผู้ใช้ → ปุ่มเปลี่ยนภาษา */}
        <div className="flex items-center space-x-4 ml-auto">
          <div
            onClick={logout}
            className="cursor-pointer bg-[#0a6dc2] border border-white rounded-3xl py-2 px-4"
          >
            <p
              data-testid="text-userInfo-sidebar"
              className="text-white text-md font-medium"
            >
              {user || "Guest"}
            </p>
          </div>
          <LanguageSelector />
        </div>
      </header>

      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 h-full w-[300px] bg-[#184591] text-white transform transition-transform duration-300 pt-16
        ${isOpen ? "translate-x-0" : "-translate-x-full"}`}
      >
        <div className="h-full overflow-y-auto">
          <ul className="space-y-2 px-3 mt-5 text-md font-medium">
            {menuItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  data-testid={`sidebar-link-${item.path.replace("/", "")}`}
                  className={`flex items-center gap-2 p-2 rounded cursor-pointer transition-colors duration-200
                  ${
                    location.pathname === item.path
                      ? "active-link"
                      : "hover-link"
                  }`}
                >
                  <span
                    className={
                      location.pathname === item.path ? "text-[#0860ad]" : ""
                    }
                  >
                    {item.icon}
                  </span>
                  <span>{item.label}</span>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </aside>
    </>
  );
}

export default Sidebar;
