import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import App from "./App";
import "./utils/i18n";
import { AuthProvider } from "./contexts/AuthContext";

// PWA Register
import { registerSW } from "virtual:pwa-register";
registerSW();

// Import CSS
import "./index.css";
import "./styles/global.css";

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <App />
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>
);
