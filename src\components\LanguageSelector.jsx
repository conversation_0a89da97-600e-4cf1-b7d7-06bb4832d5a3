import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import usFlag from "../assets/us.png";
import jpFlag from "../assets/jp.png";

const languages = [
  { code: "en", label: "English", flag: usFlag },
  { code: "jp", label: "日本語", flag: jpFlag },
];

const LanguageSelector = () => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const currentLang =
    languages.find((lang) => lang.code === i18n.language) || languages[0];

  const handleChange = (langCode) => {
    i18n.changeLanguage(langCode);
    setIsOpen(false);
  };

  return (
    <div className="relative inline-block text-right z-50 p-4">
      <button
        data-testid="language-selector-toggle"
        name="languageSelectorToggle"
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex justify-center items-center gap-2 w-full px-4 py-2 bg-white text-black text-sm font-medium border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
      >
        <img
          src={currentLang.flag}
          alt={currentLang.label}
          className="w-5 h-5 rounded-full object-contain"
        />
        {currentLang.label}
      </button>

      {isOpen && (
        <div className="origin-top-left absolute right-0 mt-2 w-40 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
          <div className="py-1">
            {languages.map((lang) => (
              <button
                key={lang.code}
                data-testid={`language-option-${lang.code}`}
                type="button"
                onClick={() => handleChange(lang.code)}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <img
                  src={lang.flag}
                  alt={lang.label}
                  className="w-5 h-5 rounded-full object-contain"
                />
                {lang.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
