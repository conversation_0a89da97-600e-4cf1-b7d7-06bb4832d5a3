import { useEffect, useState } from "react";
import { useTool } from "../../../contexts/ToolContext";
import NumericKeypad from "../Popups/NumericKeypad";

function DimensionTool({ stageRef, shapes, setShapes, showToolbar }) {
  const { activeTool } = useTool();
  const [showKeypad, setShowKeypad] = useState(false);
  const [selectedDimension, setSelectedDimension] = useState(null);
  const [currentDimensionValue, setCurrentDimensionValue] = useState("");

  useEffect(() => {
    if (!stageRef || !stageRef.current) return;
    if (showToolbar) return; // Skip if toolbar is shown

    const stage = stageRef.current;

    // Add a click handler to the entire stage
    const handleStageClick = (e) => {
      // If we're in toolbar mode or not in selection mode, ignore clicks
      if (showToolbar || activeTool !== null) return;

      // Get the clicked target
      const clickedOn = e.target;

      // Find the parent group if it exists
      let group = clickedOn;
      while (group && group.getClassName() !== "Group") {
        group = group.getParent();
      }

      if (!group) return;

      // Check if this is a dimension group by ID
      if (group.id() && group.id().startsWith("dimension-")) {
        const dimensionId = group.id().replace("dimension-", "");
        const dimension = shapes.find(
          (shape) => shape.id === dimensionId && shape.type === "dimension"
        );

        if (dimension && dimension.isEditable) {
          setSelectedDimension(dimension);
          setCurrentDimensionValue(dimension.text?.toString() || "");
          setShowKeypad(true);
        }
      }
    };

    // Remove existing click handlers to prevent duplicates
    stage.off("click");

    // Add the click handler
    stage.on("click", handleStageClick);

    return () => {
      // Clean up event listener
      stage.off("click");
    };
  }, [stageRef, shapes, activeTool]);

  const handleKeypadConfirm = (value) => {
    if (selectedDimension) {
      // Create a new array with the updated shape
      const updatedShapes = [...shapes];
      const index = updatedShapes.findIndex(
        (shape) => shape.id === selectedDimension.id
      );

      if (index !== -1) {
        // Convert to number if possible
        const numericValue = !isNaN(parseFloat(value))
          ? parseFloat(value)
          : value;

        // Preserve the font size when updating
        updatedShapes[index] = {
          ...updatedShapes[index],
          text: numericValue,
          fontSize: updatedShapes[index].fontSize || 18, // Preserve existing font size
        };

        // Update the shapes state directly
        setShapes(updatedShapes);

        // Force redraw of the stage
        if (stageRef.current) {
          const layer = stageRef.current.findOne("Layer");
          if (layer) {
            layer.batchDraw();
          }
        }
      }
    }

    setShowKeypad(false);
    setSelectedDimension(null);
  };

  return (
    <>
      {showKeypad && (
        <NumericKeypad
          initialValue={currentDimensionValue}
          onConfirm={(value) => {
            handleKeypadConfirm(value);
            // Hide the keypad after confirmation
            setShowKeypad(false);
            setCurrentDimensionValue("");
          }}
          onCancel={() => {
            setShowKeypad(false);
            setCurrentDimensionValue("");
          }}
        />
      )}
    </>
  );
}

export default DimensionTool;

