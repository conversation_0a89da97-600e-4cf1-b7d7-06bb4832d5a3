import React from "react";
import { FaTimes } from "react-icons/fa";
import { useTranslation } from "react-i18next";

function CancelButton({ dataTestId, onClick }) {
  const { t } = useTranslation();

  return (
    <button
      data-testid={dataTestId}
      name="button-cancel"
      type="button"
      onClick={onClick}
      className="flex items-center gap-2 px-6 py-2 rounded-md border border-red-500 bg-white hover:bg-red-300"
    >
      <FaTimes className="text-gray-600 text-xl" />
      <span className="text-gray-600 text-lg">{t("action.cancel")}</span>
    </button>
  );
}

export default CancelButton;
