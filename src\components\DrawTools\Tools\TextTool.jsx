import { useRef, useState, useEffect } from "react";
import Kon<PERSON> from "konva";
import { useUndo } from "../../../contexts/UndoContext";
import { v4 as uuidv4 } from "uuid";
import { useTool } from "../../../contexts/ToolContext";

function TextTool({ triggerAdd, stageRef }) {
  const textareaRef = useRef(null);
  const { activeTool } = useTool();
  const { addAction, boxes, setBoxes } = useUndo();
  const [editingId, setEditingId] = useState(null);
  const [editingText, setEditingText] = useState("");
  const [editingPos, setEditingPos] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (triggerAdd > 0 && stageRef && stageRef.current) {
      const newBox = {
        id: uuidv4(),
        x: 150 + boxes.length * 30,
        y: 150 + boxes.length * 30,
        text: "",
        fontSize: 18,
      };

      addAction({ type: "add-text", payload: newBox, shapeType: "text" });

      setBoxes((prev) => [...prev, newBox]);
      setEditingId(newBox.id);
      setEditingText(newBox.text);
      setEditingPos({ x: newBox.x, y: newBox.y });
    }
  }, [triggerAdd]);

  useEffect(() => {
    if (editingId && textareaRef.current) {
      textareaRef.current.focus();
      const len = textareaRef.current.value.length;
      textareaRef.current.setSelectionRange(len, len);
    }
  }, [editingId]);

  const handleInputChange = (e) => {
    setEditingText(e.target.value);
  };

  const handleInputBlur = () => {
    setBoxes((prev) =>
      prev.map((box) =>
        box.id === editingId
          ? {
              ...box,
              text: editingText,
              x: editingPos.x, // Save the current editing position
              y: editingPos.y,
            }
          : box
      )
    );
    setEditingId(null);
  };

  const handleTextClick = (box) => {
    // Find the actual current position of the text box on the stage
    if (stageRef && stageRef.current) {
      const stage = stageRef.current;
      const layer = stage.findOne("Layer");
      if (layer) {
        const textGroup = layer.findOne(`#text-box-${box.id}`);
        if (textGroup) {
          // Get the current position from the Konva node
          const currentPos = textGroup.position();
          setEditingPos({ x: currentPos.x, y: currentPos.y });
        } else {
          // Fallback to the position in state if node not found
          setEditingPos({ x: box.x, y: box.y });
        }
      }
    } else {
      // Fallback to the position in state if stage not available
      setEditingPos({ x: box.x, y: box.y });
    }

    setEditingId(box.id);
    setEditingText(box.text);
  };

  // Draw text boxes on the stage
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    // Only clear standalone text boxes (not part of pasted groups)
    const textGroups = layer.find(".text-box-group");
    textGroups.forEach((group) => {
      // Skip text boxes that are part of pasted groups
      if (group.hasName && group.hasName("pasted-shape")) {
        return;
      }

      const textId = group.id().replace("text-box-", "");
      // Only destroy text boxes that aren't in our state array
      if (!boxes.some((b) => b.id.toString() === textId)) {
        group.destroy();
      }
    });

    // Import Konva dynamically to avoid React component issues
    if (!Konva) return;

    // Add or update text boxes that aren't being edited and aren't part of pasted groups
    boxes.forEach((box) => {
      if (editingId === box.id) return;

      // Skip rendering text boxes that are part of groups (handled by SelectionTool)
      if (box.groupId) return;

      // Check if this text box already exists on the stage
      const existingGroup = layer.findOne(`#text-box-${box.id}`);
      if (existingGroup) {
        // Update position
        existingGroup.position({
          x: box.x,
          y: box.y,
        });

        // Update text content and font size
        const textNode = existingGroup.findOne("Text");
        if (textNode) {
          textNode.text(box.text);
          textNode.fontSize(box.fontSize || 18);

          // Add click handler for text editing when Selection tool is active
          textNode.off("click"); // Remove previous handlers
          textNode.on("click", () => {
            // Only allow editing when Selection tool is active (activeTool is null)
            if (activeTool === null) {
              handleTextClick(box);
            }
          });
        }

        // Update rectangle appearance
        const rectNode = existingGroup.findOne("Rect");
        if (rectNode) {
          const hasText = box.text.trim() !== "";
          rectNode.stroke(hasText ? "" : "dodgerblue");

          // Add click handler for rectangle too
          rectNode.off("click"); // Remove previous handlers
          rectNode.on("click", () => {
            // Only allow editing when Selection tool is active
            if (activeTool === null) {
              handleTextClick(box);
            }
          });
        }

        return;
      }

      // Create new text box
      const group = new Konva.Group({
        x: box.x,
        y: box.y,
        draggable: true,
        name: "text-box-group",
        id: `text-box-${box.id}`,
      });

      // Add drag handlers
      group.on("dragstart", function () {
        this.moveToTop();
      });

      group.on("dragend", function () {
        const newPos = { x: this.x(), y: this.y() };
        setBoxes((prev) =>
          prev.map((b) => (b.id === box.id ? { ...b, ...newPos } : b))
        );
      });

      // Create rectangle and text elements
      const hasText = box.text.trim() !== "";
      const rect = new Konva.Rect({
        width: 200,
        height: 60,
        stroke: hasText ? "" : "dodgerblue",
        strokeWidth: 1,
        cornerRadius: 4,
      });

      const text = new Konva.Text({
        text: box.text,
        fontSize: box.fontSize || 18,
        padding: 10,
        fill: "black",
        width: 200,
        height: 60,
        verticalAlign: "middle",
      });

      // Add click handlers for both text and rectangle
      text.on("click", () => {
        // Only allow editing when Selection tool is active
        if (activeTool === null) {
          handleTextClick(box);
        }
      });

      rect.on("click", () => {
        // Only allow editing when Selection tool is active
        if (activeTool === null) {
          handleTextClick(box);
        }
      });

      group.add(rect);
      group.add(text);
      layer.add(group);
    });

    layer.draw();
  }, [boxes, editingId, activeTool]);

  return (
    <div style={{ position: "relative" }}>
      {editingId && (
        <textarea
          ref={textareaRef}
          value={editingText}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          style={{
            position: "absolute",
            top: editingPos.y,
            left: editingPos.x - 11.8,
            fontSize: "18px",
            padding: "4px",
            width: "200px",
            height: "60px",
            border: "2px dashed dodgerblue",
            background: "#ffffff",
            outline: "none",
            resize: "none",
            overflow: "hidden",
            caretColor: "black",
            animation: "blink-caret 1s step-end infinite",
            zIndex: 1000,
          }}
        />
      )}

      <style>{`
        @keyframes blink-caret {
          0%, 100% { caret-color: transparent; }
          50% { caret-color: black; }
        }
      `}</style>
    </div>
  );
}

export default TextTool;
