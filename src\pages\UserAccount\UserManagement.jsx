import { useNavigate } from "react-router-dom";
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";
import { useTranslation } from "react-i18next";

function UserManagement() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const handleCancel = () => {
    navigate("/user-account");
  };
  return (
    <div className="max-w-6xl mx-auto px-4">
      <h2
        data-testid="text-createFormUser-create"
        name="textcreateFormUsercreate"
        className="text-2xl font-bold mb-6"
      >
        {t("userAccount.userAdd")}
      </h2>
      <form className="grid grid-cols-1 gap-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Row 1 */}
          <div>
            <label
              data-testid="text-createFormUser-name1"
              name="textcreateFormUsername1"
              className="block font-semibold mb-1"
            >
              {t("userAccount.name")}
            </label>
            <select
              data-testid="select-createFormUser-name1"
              name="selectcreateFormUsername1"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option></option>
            </select>
          </div>

          <div>
            <label
              data-testid="text-createFormUser-branch"
              name="textcreateFormUserbranch"
              className="block font-semibold mb-1"
            >
              {t("userAccount.branch")}
            </label>
            <select
              data-testid="select-createFormUser-branch"
              name="selectcreateFormUserbranch"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option></option>
            </select>
          </div>

          {/* Row 2 */}
          <div>
            <label
              data-testid="text-createFormUser-userid"
              name="textcreateFormUseruserid"
              className="block font-semibold mb-1"
            >
              {t("userAccount.userID")}
            </label>
            <input
              data-testid="input-createFormUser-userid"
              name="inputcreateFormUseruserid"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            />
          </div>
          <div>
            <label
              data-testid="text-createFormUser-email"
              name="textcreateFormUseremail"
              className="block font-semibold mb-1"
            >
              {t("userAccount.email")}
            </label>
            <input
              data-testid="input-createFormUser-email"
              name="inputcreateFormUseremail"
              type="email"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            />
          </div>

          {/* Row 3 */}
          <div>
            <label
              data-testid="text-createFormUser-department"
              name="textcreateFormUserdepartment"
              className="block font-semibold mb-1"
            >
              {t("userAccount.department")}
            </label>
            <select
              data-testid="select-createFormUser-department"
              name="selectcreateFormUserdepartment"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option></option>
            </select>
          </div>

          {/* Row 4 */}
          <div>
            <label
              data-testid="text-createFormUser-post"
              name="textcreateFormUserpost"
              className="block font-semibold mb-1"
            >
              {t("userAccount.post")}
            </label>
            <select
              data-testid="select-createFormUser-post"
              name="selectcreateFormUserpost"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option></option>
            </select>
          </div>

          {/* Row 5 */}
          <div>
            <label
              data-testid="text-createFormUser-name2"
              name="textcreateFormUsername2"
              className="block font-semibold mb-1"
            >
              {t("userAccount.name")}
            </label>
            <input
              data-testid="input-createFormUser-name2"
              name="input-createFormUser-name2"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            />
          </div>
          <div>
            <label
              data-testid="textcreateFormUsersurname"
              name="textcreateFormUsersurname"
              className="block font-semibold mb-1"
            >
              {t("userAccount.surName")}
            </label>
            <input
              data-testid="input-createFormUser-surname"
              name="inputcreateFormUsersurname"
              type="text"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            />
          </div>
          {/* Row 6 */}
          <div>
            <label
              data-testid="text-createFormUser-password"
              name="textcreateFormUserpassword"
              className="block font-semibold mb-1"
            >
              {t("userAccount.password")}
            </label>
            <input
              data-testid="input-createFormUser-password"
              name="inputcreateFormUserpassword"
              type="password"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            />
          </div>
          <div>
            <label
              data-testid="text-createFormUser-confirmpassword"
              name="textcreateFormUserconfirmpassword"
              className="block font-semibold mb-1"
            >
              {t("userAccount.confirmPassword")}
            </label>
            <input
              data-testid="input-createFormUser-confirmpassword"
              name="inputcreateFormUserconfirmpassword"
              type="password"
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            />
          </div>
        </div>
      </form>

      <div className="mt-6 flex gap-4">
        <Savebutton
          dataTestId="button-createFormUser-saveuser"
          name="buttoncreateFormUsersaveuser"
        />
        <Canclebutton
          dataTestId="button-createFormUser-cancleuser"
          name="buttoncreateFormUsercancleuser"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default UserManagement;
