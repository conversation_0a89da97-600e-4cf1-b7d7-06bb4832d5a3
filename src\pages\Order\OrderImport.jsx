import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";
// React-icons
import { FaSearch } from "react-icons/fa";

// Mock data for orders
const mockOrders = [
  {
    orderDate: "04/12/2544",
    orderNo: "002040448",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "<PERSON>",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP",
    pi: 10.599,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "00222300448",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "John Doe",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP99",
    pi: 10.5,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "00212200448",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "John Doe",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya99",
    rank: "VIP",
    pi: 10.5,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "0020753740448",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "John Doe",
    deliveryRoute: "Route A99",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP",
    pi: 10.5,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "0025473247500448",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "John Doe99",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP",
    pi: 10.5,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "0020044121248",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-99",
    recipientContact: "John Doe",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP",
    pi: 10.5,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "00278689800448",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura99",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "John Doe",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP",
    pi: 10.5,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "0020785240448",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho99",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "John Doe",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP",
    pi: 10.5,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "002004124448",
    customerAbbr: "SA Engineering Co., Ltd.99",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "John Doe",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP",
    pi: 10.5,
  },
  {
    orderDate: "04/12/2544",
    orderNo: "0020045524899",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    contactName: "Okumura",
    specifiedDeliveryDate: "2025-05-10",
    recipientContact: "John Doe",
    deliveryRoute: "Route A",
    deliveryDestinationAbbreviation: "Ikagaya",
    rank: "VIP",
    pi: 10.5,
  },
  {
    orderDate: "05/12/2599",
    orderNo: "0020044777899",
    customerAbbr: "SA Engineering Co., Ltd.99",
    siteName: "Ikagaya Midoricho99",
    contactName: "Okumura99",
    specifiedDeliveryDate: "2025-05-1099",
    recipientContact: "John Doe99",
    deliveryRoute: "Route A99",
    deliveryDestinationAbbreviation: "Ikagaya99",
    rank: "VIP99",
    pi: 99,
  },
];

function OrderImport() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [searchField, setSearchField] = useState("orderNo");

  const orders = mockOrders;
  const [filteredOrders, setFilteredOrders] = useState(orders);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 8;

  // Calculate total pages
  const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);

  // Get current page data
  const paginatedOrders = filteredOrders.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateOrder = () => {
    navigate("/create-order");
  };

  const handleEditOrder = (orderNo) => {
    const orderToEdit = mockOrders.find((item) => item.orderNo === orderNo);
    navigate(`/edit-order/${orderNo}`, { state: { orderToEdit } });
  };

  useEffect(() => {
    const filtered = orders.filter((item) =>
      item[searchField]
        ?.toString()
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
    );
    setFilteredOrders(filtered);
    setCurrentPage(0);
  }, [searchTerm, searchField, orders]);

  return (
    <div className="w-full">
      {/* File Upload Section */}
      {/* <div className="mb-4 bg-gray-200 border border-gray-300 py-2 px-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
        <label className="cursor-pointer inline-block border border-gray-400 px-4 py-2 shadow-md bg-white hover:bg-gray-100 text-center w-full sm:w-auto sm:min-w-[240px] lg:w-[300px] rounded-md">
          {t("importOrder.uploadInfo1")}
          <input
            data-testid="input-selectfile-order"
            name="inputSelectfileorder"
            type="file"
            accept=".xls,.xlsx,.xlsm"
            className="hidden"
            onChange={(e) => {
              const file = e.target.files[0];
              if (file) {
                console.log("Selected file:", file);
              }
            }}
          />
        </label>

        <span
          data-testid="text-dragfile-order"
          name="textDragfileorder"
          className="text-sm text-gray-700 whitespace-nowrap"
        >
          {t("importOrder.uploadInfo2")}
        </span>
      </div>

      <div className="flex justify-start -mt-2">
        <button
          data-testid="button-upload-order"
          name="buttonUploadorder"
          className="bg-green-300 hover:bg-green-200 text-green-800 font-medium py-2 px-6 rounded-md shadow-md transition-colors duration-200"
        >
          {t("importOrder.upload")}
        </button>
      </div> */}
      <div className="flex items-center mb-3 justify-between">
        <h1 className="text-xl font-bold sm:mb-0 sm:ml-0 lg:ml-20">
          {t("importOrder.orderImport")}
        </h1>

        <button
          data-testid="button-createForm-order"
          name="buttonCreateFormorder"
          className="btn-create"
          onClick={handleCreateOrder}
        >
          {t("action.create")}
        </button>
      </div>

      {/* Header Section */}
      <div className="flex flex-row items-center mb-3 gap-2">
        <div className="relative w-full md:w-[300px] lg:w-[600px]">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            data-testid="input-search-order"
            name="inputSearchorder"
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={t("importOrder.searchPlace")}
            className="w-full pl-10 pr-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <select
          data-testid="select-search-order"
          name="selectSearchorder"
          value={searchField}
          onChange={(e) => setSearchField(e.target.value)}
          className="w-[200px] px-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="orderDate">{t("importOrder.orderDate")}</option>
          <option value="orderNo">{t("importOrder.orderNo")}</option>
          <option value="specifiedDeliveryDate">
            {t("importOrder.specifiedDeliveryDate")}
          </option>
          <option value="customerAbbr">{t("importOrder.customerAbb")}</option>
          <option value="siteName">{t("importOrder.siteName")}</option>
          <option value="recipientContact">
            {t("importOrder.recipientContact")}
          </option>
          <option value="deliveryRoute">
            {t("importOrder.deliveryRoute")}
          </option>
          <option value="deliveryDestinationAbbreviation">
            {t("importOrder.deliveryDestinationAbbreviation")}
          </option>
          <option value="contactName">{t("importOrder.contactName")}</option>
          <option value="rank">{t("importOrder.rankName")}</option>
          <option value="pi">{t("importOrder.piPercent")}</option>
        </select>
      </div>

      {/* Table Section */}
      <div className="overflow-x-auto">
        <table
          data-testid="table-order"
          name="tableOrder"
          className="w-full bg-white border-collapse text-sm"
        >
          <thead className="text-sm">
            <tr className="bg-[#4472C4] text-white">
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.orderDate")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.orderNo")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.specifiedDeliveryDate")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.customerAbb")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.siteName")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.recipientContact")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.deliveryRoute")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px] text-xs">
                {t("importOrder.deliveryDestinationAbbreviation")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.contactName")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.rankName")}
              </th>
              <th className="py-2 px-2 text-left border border-white min-w-[50px]">
                {t("importOrder.piPercent")}
              </th>
              <th className="py-2 px-2 text-center border border-white min-w-[50px]">
                {t("importOrder.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedOrders.map((order, index) => (
              <tr
                key={order.orderNo}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="py-1 px-2 border border-white">
                  {order.orderDate}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.orderNo}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.specifiedDeliveryDate}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.customerAbbr}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.siteName}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.recipientContact}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.deliveryRoute}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.deliveryDestinationAbbreviation}
                </td>
                <td className="py-1 px-2 border border-white">
                  {order.contactName}
                </td>
                <td className="py-1 px-2 border border-white">{order.rank}</td>
                <td className="py-1 px-2 border border-white">{order.pi}</td>
                <td className="py-1 px-2 border border-white text-center">
                  <div className="flex justify-center gap-2">
                    <EditButton
                      dataTestId="button-edit-order"
                      name="buttonEditorder"
                      onClick={() => handleEditOrder(order.orderNo)}
                    />
                    <DeleteButton
                      dataTestId="button-delete-order"
                      name="buttonDeleteorder"
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedOrders.length === 0 && (
              <tr>
                <td colSpan="12" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredOrders.length}
      />
    </div>
  );
}

export default OrderImport;
