import { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

// React-icons
import { AiTwotoneCalendar } from "react-icons/ai";

function CreateOrder() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const orderDateRef = useRef(null);
  const specifiedDeliveryDateRef = useRef(null);

  const [formData, setFormData] = useState({
    orderDate: null,
    orderNo: "",
    specifiedDeliveryDate: null,
    customerAbbr: "",
    siteName: "",
    contactName: "",
    deliveryDate: "",
    recipientContact: "",
    deliveryRoute: "",
    deliveryDestinationAbbreviation: "",
    rank: "",
    pi: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleOpenDatePicker = (ref) => {
    if (ref.current) {
      ref.current.setOpen(true);
    }
  };

  const handleSave = () => {
    console.log("Form data:", formData);
    // Add save logic here
  };

  const handleCancel = () => {
    navigate("/import-orders");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-createFormOrder-order"
        className="text-2xl font-bold mb-4"
      >
        {t("createOrder.orderAdd")}
      </h1>

      <div className="grid grid-cols-2 gap-2.5">
        {/* First Row */}
        <div>
          <label
            data-testid="text-createFormOrder-orderDate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.orderDate")}
          </label>
          <div className="relative w-full">
            <DatePicker
              data-testid="datepicker-createFormOrder-orderDate"
              ref={orderDateRef}
              wrapperClassName="w-full"
              selected={formData.orderDate}
              onChange={(date) => setFormData({ ...formData, orderDate: date })}
              popperPlacement="bottom-start"
              portalId="root-portal"
              inline={false}
              dateFormat="dd/MM/yyyy"
              isClearable={formData.orderDate !== null}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {!formData.orderDate && (
              <AiTwotoneCalendar
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                size={18}
                onClick={() => handleOpenDatePicker(orderDateRef)}
              />
            )}
          </div>
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-orderNo"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.orderNo")}
          </label>
          <input
            data-testid="input-createFormOrder-orderNo"
            type="text"
            name="orderNo"
            value={formData.orderNo}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Second Row */}
        <div>
          <label
            data-testid="text-createFormOrder-specifiedDeliveryDate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.specifiedDeliveryDate")}
          </label>
          <div className="relative w-full">
            <DatePicker
              data-testid="datepicker-createFormOrder-specifiedDeliveryDate"
              ref={specifiedDeliveryDateRef}
              wrapperClassName="w-full"
              selected={formData.specifiedDeliveryDate}
              onChange={(date) =>
                setFormData({ ...formData, specifiedDeliveryDate: date })
              }
              dateFormat="dd/MM/yyyy"
              isClearable={formData.specifiedDeliveryDate !== null}
              className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {!formData.specifiedDeliveryDate && (
              <AiTwotoneCalendar
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-600 cursor-pointer z-10"
                size={18}
                onClick={() => handleOpenDatePicker(specifiedDeliveryDateRef)}
              />
            )}
          </div>
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-customerAbbr"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.customerAbb")}
          </label>
          <input
            data-testid="input-createFormOrder-customerAbbr"
            type="text"
            name="customerAbbr"
            value={formData.customerAbbr}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Third Row */}
        <div>
          <label
            data-testid="text-createFormOrder-siteName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.siteName")}
          </label>
          <input
            data-testid="input-createFormOrder-siteName"
            type="text"
            name="siteName"
            value={formData.siteName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label className="block text-base font-medium text-gray-700 mb-2">
            {t("createOrder.recipientContact")}
          </label>
          <input
            type="text"
            name="recipientContact"
            value={formData.recipientContact}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Fourth Row */}
        <div>
          <label
            data-testid="text-createFormOrder-deliveryRoute"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.deliveryRoute")}
          </label>
          <input
            data-testid="input-createFormOrder-deliveryRoute"
            type="text"
            name="deliveryRoute"
            value={formData.deliveryRoute}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-deliveryDestinationAbbreviation"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.deliveryDestinationAbbreviation")}
          </label>
          <input
            data-testid="input-createFormOrder-deliveryDestinationAbbreviation"
            type="text"
            name="deliveryDestinationAbbreviation"
            value={formData.deliveryDestinationAbbreviation}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Fifth Row */}
        <div>
          <label
            data-testid="text-createFormOrder-contactName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.contactName")}
          </label>
          <input
            data-testid="input-createFormOrder-contactName"
            type="text"
            name="contactName"
            value={formData.contactName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-rank"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.rank")}
          </label>
          <select
            data-testid="select-createFormOrder-rank"
            name="rank"
            value={formData.rank}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="rank1">Rank 1</option>
            <option value="rank2">Rank 2</option>
            <option value="rank3">Rank 3</option>
          </select>
        </div>

        <div>
          <label
            data-testid="text-createFormOrder-pi"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("createOrder.pi")}
          </label>
          <input
            data-testid="input-createFormOrder-pi"
            type="number"
            name="pi"
            value={formData.pi}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-createFormOrder-save"
          onClick={handleSave}
        />
        <CancelButton
          dataTestId="button-createFormOrder-cancel"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default CreateOrder;
