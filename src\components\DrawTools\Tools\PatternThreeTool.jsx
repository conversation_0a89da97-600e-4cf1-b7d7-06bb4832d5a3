import { v4 as uuidv4 } from "uuid";

// Default dimensions for Pattern 3
export const DEFAULT_VERTICAL_LENGTH = 100;
export const DEFAULT_HORIZONTAL_LENGTH = 200;
export const DEFAULT_VERTICAL_SEGMENT = 20;

export function createPatternThree(dimensions, stageRef) {
  const stage = stageRef.current;
  const centerX = stage.width() / 2;
  const centerY = stage.height() / 2;

  const bracketHeight = 250;
  const bracketWidth = 40;
  const bottomBracketWidth = 400;
  const bottomBracketHeight = 40;

  return [
    // Vertical bracket on the right
    {
      id: uuidv4(),
      type: "polyline",
      points: [
        centerX + 100,
        centerY - bracketHeight / 2,
        centerX + 100,
        centerY + bracketHeight / 2,
      ],
      stroke: "black",
      strokeWidth: 2,
    },
    // Top segment of right bracket
    {
      id: uuidv4(),
      type: "polyline",
      points: [
        centerX + 100,
        centerY - bracketHeight / 2,
        centerX + 100 + bracketWidth,
        centerY - bracketHeight / 2,
      ],
      stroke: "black",
      strokeWidth: 2,
    },
    // Bottom segment of right bracket
    {
      id: uuidv4(),
      type: "polyline",
      points: [
        centerX + 100,
        centerY + bracketHeight / 2,
        centerX + 100 + bracketWidth,
        centerY + bracketHeight / 2,
      ],
      stroke: "black",
      strokeWidth: 2,
    },
    // Diagonal red dashed line (top-left to bottom-right)
    {
      id: uuidv4(),
      type: "dash-line",
      points: [
        centerX + 120 - bracketWidth / 2,
        centerY - bracketHeight / 2,
        centerX + 120 + bracketWidth / 2,
        centerY + bracketHeight / 2,
      ],
      stroke: "red",
      strokeWidth: 2,
      dash: [6, 3],
      dashEnabled: true,
      visibleOnlyWithDashTool: true,
    },
    // Diagonal red dashed line (bottom-left to top-right)
    {
      id: uuidv4(),
      type: "dash-line",
      points: [
        centerX + 120 - bracketWidth / 2,
        centerY + bracketHeight / 2,
        centerX + 120 + bracketWidth / 2,
        centerY - bracketHeight / 2,
      ],
      stroke: "red",
      strokeWidth: 2,
      dash: [6, 3],
      dashEnabled: true,
      visibleOnlyWithDashTool: true,
    },
    {
      id: uuidv4(),
      type: "dash-line",
      points: [
        centerX + 120 + bracketWidth / 2,
        centerY - bracketHeight / 2,
        centerX + 120 + bracketWidth / 2,
        centerY + bracketHeight / 2,
      ],
      stroke: "red",
      strokeWidth: 2,
      dash: [6, 3],
      dashEnabled: true,
      visibleOnlyWithDashTool: true,
    },
    // Bottom horizontal bracket
    {
      id: uuidv4(),
      type: "polyline",
      points: [
        centerX - bottomBracketWidth / 2,
        centerY + 220,
        centerX + bottomBracketWidth / 2,
        centerY + 220,
      ],
      stroke: "black",
      strokeWidth: 2,
    },
    // Left segment of bottom bracket
    {
      id: uuidv4(),
      type: "polyline",
      points: [
        centerX - bottomBracketWidth / 2,
        centerY + 220,
        centerX - bottomBracketWidth / 2,
        centerY + 220 - bottomBracketHeight,
      ],
      stroke: "black",
      strokeWidth: 2,
    },
    // Right segment of bottom bracket
    {
      id: uuidv4(),
      type: "polyline",
      points: [
        centerX + bottomBracketWidth / 2,
        centerY + 220,
        centerX + bottomBracketWidth / 2,
        centerY + 220 - bottomBracketHeight,
      ],
      stroke: "black",
      strokeWidth: 2,
    },
    // Horizontal arrow pointing right
    {
      id: uuidv4(),
      type: "arrow",
      points: [centerX - 70, centerY - 20, centerX + 10, centerY - 20],
      stroke: "black",
      fill: "black",
      strokeWidth: 2,
      pointerLength: 10,
      pointerWidth: 10,
    },
    // Vertical arrow pointing up
    {
      id: uuidv4(),
      type: "arrow",
      points: [centerX + 100, centerY + 300, centerX + 100, centerY + 250],
      stroke: "black",
      fill: "black",
      strokeWidth: 2,
      pointerLength: 10,
      pointerWidth: 10,
    },
    // Top right corner number
    {
      id: uuidv4(),
      type: "text",
      x: centerX + 100 + bracketWidth - 25,
      y: centerY - bracketHeight / 2 - 20,
      text: 20,
      fontSize: 16,
      position: "topRight",
      isEditable: true,
    },
    // Top Bottom right corner number
    {
      id: uuidv4(),
      type: "text",
      x: centerX + 100 + bracketWidth - 25,
      y: centerY + bracketHeight / 2 + 10,
      text: 20,
      fontSize: 16,
      position: "bottomRight",
      isEditable: true,
    },
    // Bottom left corner number
    {
      id: uuidv4(),
      type: "text",
      x: centerX - bottomBracketWidth / 2 - 30,
      y: centerY + 220 - 25,
      text: 20,
      fontSize: 16,
      position: "bottomLeft",
      isEditable: true,
    },
    // Bottom right corner number
    {
      id: uuidv4(),
      type: "text",
      x: centerX + bottomBracketWidth / 2 + 20,
      y: centerY + 220 - 25,
      text: 20,
      fontSize: 16,
      position: "bottomRight2",
      isEditable: true,
    },
    // Vertical dimension text (middle of vertical bracket)
    {
      id: uuidv4(),
      type: "text",
      x: centerX + 100 - 40,
      y: centerY - 10,
      text: 100,
      fontSize: 16,
      position: "middle",
      isEditable: true,
    },
    // Horizontal dimension text (below bottom bracket)
    {
      id: uuidv4(),
      type: "text",
      x: centerX - 20,
      y: centerY + 220 + 25,
      text: 2000,
      fontSize: 16,
      position: "bottom",
      isEditable: true,
    },
  ];
}

export function updatePatternThree(shapes, dimensionId, newValue) {
  // Find the changed text
  const changedText = shapes.find(
    (shape) => shape.type === "text" && shape.id === dimensionId
  );

  if (!changedText) return shapes;

  // Convert to number and update the text value
  const numericValue = parseFloat(newValue) || 0;

  // Get the position of the changed text
  const position = changedText.position;

  // Update dash-lines and other elements based on which text was changed
  return shapes.map((shape) => {
    if (shape.id === dimensionId) {
      return {
        ...shape,
        text: numericValue,
      };
    }

    // Update dash-lines when dimensions change
    if (shape.type === "dash-line" && position) {
      const points = [...shape.points];

      // Update dash-lines based on which dimension was changed
      if (position === "middle" || position === "bottom") {
        // For vertical or horizontal dimension changes
        return {
          ...shape,
          points: updateDashLinePoints(
            points,
            position,
            numericValue,
            changedText
          ),
        };
      }
    }

    return shape;
  });
}

// Helper function to update dash-line points based on dimension changes
function updateDashLinePoints(points, position, value, changedText) {
  // Clone the points array
  const newPoints = [...points];

  // Update points based on which dimension was changed
  return newPoints;
}

export function centerPatternThree(shapes, stageRef) {
  if (!stageRef || !stageRef.current) return shapes;

  const stage = stageRef.current;
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Find all polyline shapes to determine pattern bounds
  const polylines = shapes.filter((shape) => shape.type === "polyline");

  if (polylines.length === 0) return shapes;

  // Calculate current pattern bounds
  let minX = Infinity,
    maxX = -Infinity,
    minY = Infinity,
    maxY = -Infinity;

  polylines.forEach((polyline) => {
    for (let i = 0; i < polyline.points.length; i += 2) {
      minX = Math.min(minX, polyline.points[i]);
      maxX = Math.max(maxX, polyline.points[i]);
      minY = Math.min(minY, polyline.points[i + 1]);
      maxY = Math.max(maxY, polyline.points[i + 1]);
    }
  });

  // Calculate pattern center and dimensions
  const patternCenterX = (minX + maxX) / 2;
  const patternCenterY = (minY + maxY) / 2;

  // Calculate the stage center
  const stageCenterX = stageWidth / 2;
  const stageCenterY = stageHeight / 2;

  // Calculate the offset to center the pattern
  const offsetX = stageCenterX - patternCenterX;
  const offsetY = stageCenterY - patternCenterY;

  // Apply the offset to all shapes, including dash-lines
  return shapes.map((shape) => {
    if (shape.type === "polyline" || shape.type === "dash-line") {
      const newPoints = [...shape.points];
      for (let i = 0; i < newPoints.length; i += 2) {
        newPoints[i] += offsetX;
        newPoints[i + 1] += offsetY;
      }
      return { ...shape, points: newPoints };
    }
    if (shape.type === "text") {
      return {
        ...shape,
        x: shape.x + offsetX,
        y: shape.y + offsetY,
      };
    }
    if (shape.type === "arrow") {
      const newPoints = [...shape.points];
      for (let i = 0; i < newPoints.length; i += 2) {
        newPoints[i] += offsetX;
        newPoints[i + 1] += offsetY;
      }
      return { ...shape, points: newPoints };
    }
    return shape;
  });
}
