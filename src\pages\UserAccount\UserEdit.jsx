import React from "react";
import { useNavigate } from "react-router-dom";
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";
import { useTranslation } from "react-i18next";
function UserEdit() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const handleCancel = () => {
    navigate("/user-account");
  };
  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2
        data-testid="text-editFormUser-edit"
        name="texteditFormUseredit"
        className="text-2xl font-bold mb-6"
      >
        {t("userAccount.userEdit")}
      </h2>
      <form className="grid grid-cols-1 gap-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Row 1 */}
          <div>
            <label
              data-testid="text-editFormUser-name1"
              name="texteditFormUsername1"
              className="block font-semibold mb-1"
            >
              {t("userAccount.name")}
            </label>
            <select
              data-testid="select-editFormUser-name1"
              name="selecteditFormUsername1"
              className="w-full border rounded px-3 py-2"
            >
              <option></option>
            </select>
          </div>

          <div>
            <label
              data-testid="text-editFormUser-branch"
              name="texteditFormUserbranch"
              className="block font-semibold mb-1"
            >
              {t("userAccount.branch")}
            </label>
            <select
              data-testid="select-editFormUser-branch"
              name="selecteditFormUserbranch"
              className="w-full border rounded px-3 py-2"
            >
              <option></option>
            </select>
          </div>

          {/* Row 2 */}
          <div>
            <label
              data-testid="text-editFormUser-userid"
              name="texteditFormUseruserid"
              className="block font-semibold mb-1"
            >
              {t("userAccount.userID")}
            </label>
            <input
              data-testid="input-editFormUser-userid"
              name="input-editFormUser-userid"
              type="text"
              className="w-full border rounded px-3 py-2"
            />
          </div>
          <div></div>

          {/* Row 3 */}
          <div>
            <label
              data-testid="text-editFormUser-department"
              name="texteditFormUserdepartment"
              className="block font-semibold mb-1"
            >
              {t("userAccount.department")}
            </label>
            <select
              data-testid="select-editFormUser-department"
              name="selecteditFormUserdepartment"
              className="w-full border rounded px-3 py-2"
            >
              <option></option>
            </select>
          </div>

          {/* Row 4 */}
          <div>
            <label
              data-testid="text-editFormUser-post"
              name="texteditFormUserpost"
              className="block font-semibold mb-1"
            >
              {t("userAccount.post")}
            </label>
            <select
              data-testid="select-editFormUser-post"
              name="sselecteditFormUserpost"
              className="w-full border rounded px-3 py-2"
            >
              <option></option>
            </select>
          </div>

          {/* Row 5 */}
          <div>
            <label
              data-testid="text-editFormUser-name2"
              name="texteditFormUsername2"
              className="block font-semibold mb-1"
            >
              {t("userAccount.name")}
            </label>
            <input
              data-testid="input-editFormUser-name2"
              name="inputeditFormUsername2"
              type="text"
              className="w-full border rounded px-3 py-2"
            />
          </div>
          <div>
            <label
              data-testid="text-editFormUser-surname"
              name="texteditFormUsersurname"
              className="block font-semibold mb-1"
            >
              {t("userAccount.surName")}
            </label>
            <input
              data-testid="input-editFormUser-surname"
              name="inputeditFormUsersurname"
              type="text"
              className="w-full border rounded px-3 py-2"
            />
          </div>
          {/* Row 6 */}
          <div>
            <label
              data-testid="text-editFormUser-password"
              name="texteditFormUserpassword"
              className="block font-semibold mb-1"
            >
              {t("userAccount.password")}
            </label>
            <input
              type="password"
              className="w-full border rounded px-3 py-2"
              data-testid="iinput-editFormUser-password"
              name="inputeditFormUserpassword"
            />
          </div>
          <div>
            <label
              data-testid="text-editFormUser-confirmpassword"
              name="texteditFormUserconfirmpassword"
              className="block font-semibold mb-1"
            >
              {t("userAccount.confirmPassword")}
            </label>
            <input
              data-testid="input-editFormUser-confirmpassword"
              name="inputeditFormUserconfirmpassword"
              type="password"
              className="w-full border rounded px-3 py-2"
            />
          </div>
        </div>
      </form>

      <div className="mt-6 flex gap-4">
        <Savebutton
          dataTestId="button-editFormUser-saveuser"
          name="buttoneditFormUsersaveuser"
        />
        <Canclebutton
          dataTestId="button-editFormUser-cancleuser"
          name="buttoneditFormUsercancleuser"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default UserEdit;
