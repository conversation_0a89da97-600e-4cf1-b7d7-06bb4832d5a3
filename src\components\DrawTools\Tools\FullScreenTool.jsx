import { useEffect } from "react";
import <PERSON>n<PERSON> from "konva";
import { useTool } from "../../../contexts/ToolContext";
import { useFullscreen } from "../../../contexts/FullscreenContext";

function FullScreenTool({ stageRef }) {
  const { activeTool, setActiveTool } = useTool();
  const { isFullscreenActive, setIsFullscreenActive } = useFullscreen();
  
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;
    
    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;
    
    // If fullscreen tool is activated
    if (activeTool === "fullscreen") {
      // Toggle frame visibility
      if (isFullscreenActive) {
        // If frame exists, remove it
        layer.find(".fullscreen-frame").forEach(frame => frame.destroy());
        layer.draw();
        setIsFullscreenActive(false);
      } else {
        // If no frame exists, create one
        createFullscreenFrame(layer);
        setIsFullscreenActive(true);
      }
      
      // Always deactivate the tool after toggling
      setActiveTool(null);
    }
    
    return () => {
      // Cleanup function - no need to remove frame here
    };
  }, [activeTool, stageRef, setActiveTool, isFullscreenActive, setIsFullscreenActive]);
  
  // Function to create the fullscreen frame
  const createFullscreenFrame = (layer) => {
    // Remove any existing frames first
    layer.find(".fullscreen-frame").forEach(frame => frame.destroy());
    
    // Calculate bounding box for all objects
    const shapes = layer.children.filter(child => 
      !child.hasName("fullscreen-frame") && 
      child.className !== "Layer"
    );
    
    if (shapes.length === 0) {
      // No shapes to frame
      return;
    }
    
    // Get bounding box for all shapes
    const box = {
      x: Infinity,
      y: Infinity,
      width: 0,
      height: 0,
      right: 0,
      bottom: 0
    };
    
    shapes.forEach(shape => {
      const rect = shape.getClientRect();
      box.x = Math.min(box.x, rect.x);
      box.y = Math.min(box.y, rect.y);
      box.right = Math.max(box.right, rect.x + rect.width);
      box.bottom = Math.max(box.bottom, rect.y + rect.height);
    });
    
    box.width = box.right - box.x;
    box.height = box.bottom - box.y;
    
    // Add padding
    const padding = 20;
    box.x -= padding;
    box.y -= padding;
    box.width += padding * 2;
    box.height += padding * 2;
    
    // Create frame
    const frame = new Konva.Rect({
      x: box.x,
      y: box.y,
      width: box.width,
      height: box.height,
      stroke: "#07a4fe",
      strokeWidth: 2,
      dash: [10, 5],
      name: "fullscreen-frame",
      listening: false
    });
    
    layer.add(frame);
    layer.draw();
  };
  
  return null;
}

export default FullScreenTool;


