import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

// React-icons
import { FaPlus, FaTimes } from "react-icons/fa";

function CreateHoleType() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [holeTypeSets, setHoleTypeSets] = useState([
    {
      holeType: "",
      size: "",
      unit: "",
      materialType: "",
      colorTypes: "",
    },
  ]);

  const handleChange = (index, field, value) => {
    const updatedSets = [...holeTypeSets];
    updatedSets[index][field] = value;
    setHoleTypeSets(updatedSets);
  };

  const handleAddSet = () => {
    setHoleTypeSets([
      ...holeTypeSets,
      {
        holeType: "",
        size: "",
        unit: "",
        materialType: "",
        colorTypes: "",
      },
    ]);
  };

  const handleDeleteSet = (index) => {
    const updatedSets = [...holeTypeSets];
    updatedSets.splice(index, 1);
    setHoleTypeSets(updatedSets);
  };

  const handleSave = () => {
    console.log("Saving hole types:", holeTypeSets);
    navigate("/hole-type");
  };

  const handleCancel = () => {
    navigate("/hole-type");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-holeType-create"
        className="text-2xl font-bold mb-6"
      >
        {t("holeType.holeAdd")}
      </h1>

      {holeTypeSets.map((set, index) => (
        <div
          key={index}
          className="mb-8 p-6 border border-gray-300 rounded-md shadow-sm relative"
        >
          {/* Delete button - only show if there's more than one form */}
          {holeTypeSets.length > 1 && (
            <button
              data-testid="button-createFormHoleType-deleteForm"
              name="buttonDeleteFormHoleType"
              type="button"
              onClick={() => handleDeleteSet(index)}
              className="absolute top-2 right-2 text-red-500 hover:text-red-700"
            >
              <FaTimes size={20} />
            </button>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            {/* Hole Type */}
            <div>
              <label
                data-testid="text-createFormHoleType-holeType"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.hole")}
              </label>
              <select
                data-testid="select-createFormHoleType-holeType"
                name="holeType"
                value={set.holeType}
                onChange={(e) =>
                  handleChange(index, "holeType", e.target.value)
                }
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value=""></option>
                <option value="Countersink">{t("holeType.counterSink")}</option>
                <option value="Round hole">{t("holeType.roundHole")}</option>
                <option value="Slot">{t("holeType.slot")}</option>
              </select>
            </div>

            {/* Size */}
            <div>
              <label
                data-testid="text-createFormHoleType-size"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.size")}
              </label>
              <input
                data-testid="input-createFormHoleType-size"
                name="size"
                type="text"
                value={set.size}
                onChange={(e) => handleChange(index, "size", e.target.value)}
                placeholder="0"
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Unit */}
            <div>
              <label
                data-testid="text-createFormHoleType-unit"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.unit")}
              </label>
              <select
                data-testid="select-createFormHoleType-unit"
                name="unit"
                value={set.unit}
                onChange={(e) => handleChange(index, "unit", e.target.value)}
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value=""></option>
                <option value="Φ">Φ</option>
              </select>
            </div>

            {/* Material Type */}
            <div>
              <label
                data-testid="text-createFormHoleType-materialType"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.materialType")}
              </label>
              <select
                data-testid="select-createFormHoleType-materialType"
                name="materialType"
                value={set.materialType}
                onChange={(e) =>
                  handleChange(index, "materialType", e.target.value)
                }
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value=""></option>
                <option value="Acrylic">Acrylic</option>
                <option value="Urethane">Urethane</option>
                <option value="Fluorine">Fluorine</option>
              </select>
            </div>

            {/* Color Types */}
            <div>
              <label
                data-testid="text-createFormHoleType-colorTypes"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.colorTypes")}
              </label>
              <select
                data-testid="select-createFormHoleType-colorTypes"
                name="colorTypes"
                value={set.colorTypes}
                onChange={(e) =>
                  handleChange(index, "colorTypes", e.target.value)
                }
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value=""></option>
                <option value="Silver">Silver</option>
                <option value="Light color">Light color</option>
                <option value="Dark color">Dark color</option>
              </select>
            </div>

            {/* Pattern */}
            <div>
              <label
                data-testid="text-createFormHoleType-pattern"
                className="block text-base font-medium text-gray-700 mb-2"
              >
                {t("holeType.pattern")}
              </label>
              <input
                data-testid="input-createFormHoleType-pattern"
                name="pattern"
                type="text"
                value={set.pattern}
                onChange={(e) => handleChange(index, "pattern", e.target.value)}
                className="w-full px-4 py-2 rounded-md shadow-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      ))}

      {/* Add button */}
      <div className="flex justify-end mb-8">
        <button
          data-testid="button-createFormHoleType-addForm"
          name="buttonAddFormHoleType"
          type="button"
          onClick={handleAddSet}
          className="w-12 h-12 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center text-white shadow-md transition-colors duration-200"
        >
          <FaPlus size={24} />
        </button>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          dataTestId="button-createFormHoleType-saveHoleType"
          onClick={handleSave}
        />
        <CancelButton
          dataTestId="button-createFormHoleType-cancelHoleType"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default CreateHoleType;
