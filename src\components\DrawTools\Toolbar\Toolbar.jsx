import { useState } from "react";

// Import Contexts
import { useTool } from "../../../contexts/ToolContext";
import { useFullscreen } from "../../../contexts/FullscreenContext";
import { useTranslation } from "react-i18next";

// Import Tools Components
import ToolButton from "./ToolButton";
import CustomFullscreenExitIcon from "../Icons/CustomFullscreenExitIcon";
import DropdownTool from "./DropdownTool";
import TextTool from "../Tools/TextTool";
import LineTool from "../Tools/LineTool";
import ArrowTool from "../Tools/ArrowTool";
import DoubleArrowTool from "../Tools/DoubleArrowTool";
import FontSizeTool from "../Tools/FontSizeTool";
import SelectionTool from "../Tools/SelectionTool";

// React-Icons
import { LiaDraftingCompassSolid } from "react-icons/lia";
import { BsTextareaT } from "react-icons/bs";
import { GoArrowDownRight, GoArrowBoth } from "react-icons/go";
import { MdFullscreen } from "react-icons/md";
import { LuLasso } from "react-icons/lu";
import { BsCircle, BsSquare } from "react-icons/bs";
import { BiFontSize } from "react-icons/bi";
import { PiCursorLight } from "react-icons/pi";
import { AiOutlineMinus } from "react-icons/ai";
import { FiChevronDown } from "react-icons/fi";
import { IoMdClose } from "react-icons/io";

// Internal Popup Components
function RoundHolePopup({ onSubmit, onCancel, initialDimensions }) {
  const [diameter, setDiameter] = useState(100);
  const [topDimension, setTopDimension] = useState(
    initialDimensions?.width !== undefined
      ? (initialDimensions.width - 100).toString()
      : 100
  );
  const [rightDimension, setRightDimension] = useState(
    initialDimensions?.height !== undefined
      ? (initialDimensions.height - 100).toString()
      : 100
  );

  const handleSubmit = () => {
    // แปลงค่า string เป็น number ก่อนส่ง (ถ้าจำเป็น)
    const parsedDiameter = parseFloat(diameter);
    const parsedTopDimension = parseFloat(topDimension);
    const parsedRightDimension = parseFloat(rightDimension);

    // เรียก onOk prop พร้อมส่งค่าที่ได้กรอกไป
    onSubmit({
      diameter: isNaN(parsedDiameter) ? 0 : parsedDiameter, // ใส่ค่าเริ่มต้นถ้า parse ไม่ได้
      topDimension: isNaN(parsedTopDimension) ? 0 : parsedTopDimension,
      rightDimension: isNaN(parsedRightDimension) ? 0 : parsedRightDimension,
    });
  };

  const handleClear = (setter) => {
    setter("");
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
      <div className="bg-white rounded-3xl shadow-xl p-6 w-full max-w-md">
        <h2 className="text-xl font-bold text-center mb-4">Round Hole</h2>
        <hr className="border-gray-300 mb-6" />

        <div className="space-y-4">
          {/* Diameter Input */}
          <div className="flex items-center justify-between">
            <label className="font-medium text-gray-800">diameter</label>
            <div className="relative w-60">
              <input
                type="number"
                value={diameter}
                onChange={(e) => setDiameter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              {diameter && (
                <button
                  onClick={() => handleClear(setDiameter)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <IoMdClose />
                </button>
              )}
            </div>
          </div>

          {/* Top Dimension Input */}
          <div className="flex items-center justify-between">
            <label className="font-medium text-gray-800">Top Dimension</label>
            <div className="relative w-60">
              <input
                type="number"
                value={topDimension}
                onChange={(e) => setTopDimension(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              {topDimension && (
                <button
                  onClick={() => handleClear(setTopDimension)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <IoMdClose />
                </button>
              )}
            </div>
          </div>

          {/* Right Dimension Input */}
          <div className="flex items-center justify-between">
            <label className="font-medium text-gray-800">Right Dimension</label>
            <div className="relative w-60">
              <input
                type="number"
                value={rightDimension}
                onChange={(e) => setRightDimension(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              {rightDimension && (
                <button
                  onClick={() => handleClear(setRightDimension)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <IoMdClose />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4 mt-8">
          <button
            onClick={onCancel}
            className="px-8 py-2 bg-gray-300 text-gray-700 rounded-full hover:bg-gray-400 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-10 py-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
}

function CornerHolePopup({ onCancel }) {
  const [width, setWidth] = useState("100");
  const [height, setHeight] = useState("100");
  const [radius, setRadius] = useState("20");
  const [topDimension, setTopDimension] = useState("100");
  const [rightDimension, setRightDimension] = useState("100");

  const handleClear = (setter) => {
    setter("");
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
      <div className="bg-white rounded-3xl shadow-xl p-6 w-full max-w-md">
        <h2 className="text-xl font-bold text-center mb-4">Corner Hole</h2>
        <hr className="border-gray-300 mb-6" />

        <div className="space-y-4">
          {/* Width Input */}
          <div className="flex items-center justify-between">
            <label className="font-medium text-gray-800">Width</label>
            <div className="relative w-60">
              <input
                type="text"
                value={width}
                onChange={(e) => setWidth(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              {width && (
                <button
                  onClick={() => handleClear(setWidth)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <IoMdClose />
                </button>
              )}
            </div>
          </div>

          {/* Height Input */}
          <div className="flex items-center justify-between">
            <label className="font-medium text-gray-800">Height</label>
            <div className="relative w-60">
              <input
                type="text"
                value={height}
                onChange={(e) => setHeight(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              {height && (
                <button
                  onClick={() => handleClear(setHeight)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <IoMdClose />
                </button>
              )}
            </div>
          </div>

          {/* Radius Input */}
          <div className="flex items-center justify-between">
            <label className="font-medium text-gray-800">Corner Radius</label>
            <div className="relative w-60">
              <input
                type="text"
                value={radius}
                onChange={(e) => setRadius(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              {radius && (
                <button
                  onClick={() => handleClear(setRadius)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <IoMdClose />
                </button>
              )}
            </div>
          </div>

          {/* Top Dimension Input */}
          <div className="flex items-center justify-between">
            <label className="font-medium text-gray-800">Top Dimension</label>
            <div className="relative w-60">
              <input
                type="text"
                value={topDimension}
                onChange={(e) => setTopDimension(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              {topDimension && (
                <button
                  onClick={() => handleClear(setTopDimension)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <IoMdClose />
                </button>
              )}
            </div>
          </div>

          {/* Right Dimension Input */}
          <div className="flex items-center justify-between">
            <label className="font-medium text-gray-800">Right Dimension</label>
            <div className="relative w-60">
              <input
                type="text"
                value={rightDimension}
                onChange={(e) => setRightDimension(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
              {rightDimension && (
                <button
                  onClick={() => handleClear(setRightDimension)}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <IoMdClose />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4 mt-8">
          <button
            onClick={onCancel}
            className="px-8 py-2 bg-gray-300 text-gray-700 rounded-full hover:bg-gray-400 transition-colors"
          >
            Cancel
          </button>
          <button className="px-10 py-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors">
            OK
          </button>
        </div>
      </div>
    </div>
  );
}

export default function ToolBar({
  stageRef,
  showToolbar,
  showAdjustSection,
  toggleToolbar,
  selectedPattern,
  dimensionValues,
  onRoundHoleSubmit,
}) {
  const { t } = useTranslation();
  const { activeTool, setActiveTool, setShowDashLines } = useTool();
  const { isFullscreenActive } = useFullscreen();
  const [triggerCount, setTriggerCount] = useState(0);
  const [showRoundHolePopup, setShowRoundHolePopup] = useState(false);
  const [showCornerHolePopup, setShowCornerHolePopup] = useState(false);

  // Add this function to check if any line tool is active
  const isLineToolActive = () => {
    const lineTools = ["solid", "dashed", "dotDash", "dotDash2"];
    return lineTools.includes(activeTool);
  };

  // Check if selection tool should be active
  const isSelectionActive = () => {
    return activeTool === null && showToolbar;
  };

  const handleAddTextBox = () => {
    setTriggerCount((prev) => prev + 1);
  };

  const handleCompassOptionSelect = (option) => {
    if (option.value === "roundHole") {
      setShowRoundHolePopup(true);
    } else if (option.value === "cornerHole") {
      setShowCornerHolePopup(true);
    } else {
      setActiveTool(option.value);
    }
  };

  const handleRoundHoleCancel = () => {
    setShowRoundHolePopup(false);
  };

  const handleCornerHoleCancel = () => {
    setShowCornerHolePopup(false);
  };

  const handleToolClick = (toolName) => {
    if (toolName === "fullscreen") {
      // Just activate the tool, the FullScreenTool will handle the toggling
      setActiveTool("fullscreen");
    } else {
      // For other tools, toggle as before
      setActiveTool(toolName === activeTool ? null : toolName);

      if (toolName === "dashed") {
        setShowDashLines(true);
      }
    }
  };

  // Function to render the appropriate dimension text based on pattern
  const renderDimensionText = () => {
    if (!selectedPattern) return "糸幅: / 長さ:  ";

    switch (selectedPattern) {
      case "Pattern 1,2":
        return "糸幅: /";
      case "Pattern 3":
        return "糸幅: / 長さ:  ";
      case "Pattern 4":
        return "糸幅: / 長さ:  ";
      case "Pattern 5":
        return "糸幅: / 長さ:  ";
      case "Pattern 6":
        return "糸幅: / 長さ:  ";
      case "Pattern 7":
        return "糸幅: / 長さ:  ";
      case "Square and Round Holes":
        const width = dimensionValues?.width ?? "";
        const height = dimensionValues?.height ?? "";
        return `糸幅: ${width} / 長さ: ${height}`;
      default:
        return "糸幅: / 長さ:  ";
    }
  };

  return (
    <>
      <div className="flex justify-between items-center px-3 absolute left-1/2 transform -translate-x-1/2 top-20 z-10 min-w-full">
        {showAdjustSection && !showToolbar ? (
          <>
            <div className="text-red-500 font-semibold max-w-md">
              <p>{t("Toolbar.adjustDes1")}</p>
              <p>{t("Toolbar.adjustDes2")}</p>
            </div>
            <div className="flex items-center">
              <button
                onClick={toggleToolbar}
                className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-6 rounded-md shadow-md"
              >
                {t("Toolbar.adjust")}
              </button>
            </div>
          </>
        ) : showToolbar ? (
          <>
            <div className="text-gray-500 text-xl">{renderDimensionText()}</div>

            <div className="flex items-center space-x-2 bg-green-500 text-white px-4 py-1 rounded-lg shadow-md">
              <ToolButton
                icon={<PiCursorLight className="w-7 h-7" />}
                onClick={() => setActiveTool(null)}
                active={activeTool === null}
                title="Selection"
              />
              <DropdownTool
                icon={<LiaDraftingCompassSolid className="w-7 h-7" />}
                options={[
                  {
                    icon: <BsCircle className="w-5 h-5" />,
                    value: "roundHole",
                    label: "Round Hole",
                  },
                  {
                    icon: <BsSquare className="w-5 h-5" />,
                    value: "cornerHole",
                    label: "Corner Hole",
                  },
                ]}
                onSelect={handleCompassOptionSelect}
                title="Compass"
              />
              <ToolButton
                icon={<GoArrowDownRight className="w-7 h-7" />}
                onClick={() => handleToolClick("arrow")}
                active={activeTool === "arrow"}
                title="Arrow"
              />
              <ToolButton
                icon={<GoArrowBoth className="w-7 h-7" />}
                onClick={() => handleToolClick("doubleArrow")}
                active={activeTool === "doubleArrow"}
                title="Double Arrow"
              />
              <DropdownTool
                icon={
                  <div className="flex items-center">
                    <AiOutlineMinus className="w-7 h-7" />
                    <FiChevronDown />
                  </div>
                }
                options={[
                  {
                    icon: <div className="w-16 h-0.5 bg-black"></div>,
                    value: "solid",
                  },
                  {
                    icon: (
                      <div className="w-16 flex items-center">
                        <div className="w-2 h-0.5 bg-black mr-1"></div>
                        <div className="w-2 h-0.5 bg-black mr-1"></div>
                        <div className="w-2 h-0.5 bg-black mr-1"></div>
                        <div className="w-2 h-0.5 bg-black mr-1"></div>
                        <div className="w-2 h-0.5 bg-black mr-1"></div>
                        <div className="w-2 h-0.5 bg-black mr-1"></div>
                      </div>
                    ),
                    value: "dashed",
                  },
                  {
                    icon: (
                      <div className="w-16 flex items-center">
                        <div className="w-4 h-0.5 bg-black mr-1"></div>
                        <div className="w-1 h-0.5 bg-black mr-1"></div>
                        <div className="w-4 h-0.5 bg-black mr-1"></div>
                        <div className="w-1 h-0.5 bg-black mr-1"></div>
                        <div className="w-1 h-0.5 bg-black mr-1"></div>
                      </div>
                    ),
                    value: "dotDash",
                  },
                  {
                    icon: (
                      <div className="w-16 flex items-center">
                        <div className="w-4 h-0.5 bg-black mr-1"></div>
                        <div className="w-1 h-0.5 bg-black mr-1"></div>
                        <div className="w-1 h-0.5 bg-black mr-1"></div>
                        <div className="w-4 h-0.5 bg-black mr-1"></div>
                        <div className="w-1 h-0.5 bg-black mr-1"></div>
                      </div>
                    ),
                    value: "dotDash2",
                  },
                ]}
                onSelect={(option) => handleToolClick(option.value)}
                title="Line Style"
                active={isLineToolActive()}
              />
              <ToolButton
                icon={
                  isFullscreenActive ? (
                    <CustomFullscreenExitIcon className="w-7 h-7" />
                  ) : (
                    <MdFullscreen className="w-7 h-7" />
                  )
                }
                onClick={() => handleToolClick("fullscreen")}
                title={isFullscreenActive ? "Exit Fullscreen" : "Fullscreen"}
              />
              <ToolButton
                icon={<LuLasso className="w-7 h-7" />}
                onClick={() => handleToolClick("lasso")}
                active={activeTool === "lasso"}
                title="Lasso"
              />
              <ToolButton
                icon={<BsTextareaT className="w-7 h-7" />}
                onClick={handleAddTextBox}
                title="Text"
              />
              <ToolButton
                icon={<BiFontSize className="w-7 h-7" />}
                onClick={() => handleToolClick("textHeight")}
                active={activeTool === "textHeight"}
                title="Text Height"
              />
            </div>
          </>
        ) : null}
      </div>

      {/* Round Hole Popup */}
      {showRoundHolePopup && (
        <RoundHolePopup
          onSubmit={(values) => {
            onRoundHoleSubmit?.(values); // เรียก callback ที่มาจาก DrawPage
            setShowRoundHolePopup(false);
          }}
          onCancel={handleRoundHoleCancel}
          initialDimensions={dimensionValues}
        />
      )}

      {/* Corner Hole Popup */}
      {showCornerHolePopup && (
        <CornerHolePopup onCancel={handleCornerHoleCancel} />
      )}

      {/* Selection Tool (active when no other tool is selected) */}
      <SelectionTool stageRef={stageRef} isActive={isSelectionActive()} />

      {/* Only render the active tool component */}
      <TextTool triggerAdd={triggerCount} stageRef={stageRef} />
      {isLineToolActive() && (
        <LineTool activeTool={activeTool} stageRef={stageRef} />
      )}
      {activeTool === "arrow" && (
        <ArrowTool activeTool={activeTool} stageRef={stageRef} />
      )}
      {activeTool === "doubleArrow" && (
        <DoubleArrowTool activeTool={activeTool} stageRef={stageRef} />
      )}
      <FontSizeTool activeTool={activeTool} stageRef={stageRef} />
    </>
  );
}
