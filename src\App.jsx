import { Routes, Route, Navigate } from "react-router-dom";
import LoginPage from "./pages/LoginPage";
import MainLayout from "./layouts/MainLayout";
import ProtectedRoute from "./routes/ProtectedRoute";
import { UndoProvider } from "./contexts/UndoContext";
import { ToolProvider } from "./contexts/ToolContext";

// Home
import HomePage from "./pages/HomePage";
// User Account
import UserManagement from "./pages/UserAccount/UserManagement";
import UserEdit from "./pages/UserAccount/UserEdit";
import UserAccount from "./pages/UserAccount/UserAccount";
// Customer Master
import CustomerMaster from "./pages/CustomerMaster/CustomerMasterPage";
import CreateCustomerMaster from "./pages/CustomerMaster/CreateCustomerMaster";
import EditCustomerMaster from "./pages/CustomerMaster/EditCustomerMaster";
// Rank
import RankPage from "./pages/Rank/RankPage";
import CreateRank from "./pages/Rank/CreateRank";
import EditRank from "./pages/Rank/EditRank";
// Order
import OrderImport from "./pages/Order/OrderImport";
import CreateOrder from "./pages/Order/CreateOrder";
import EditOrder from "./pages/Order/EditOrder";
// Project List
import ProjectlistPage from "./pages/Projectlist/ProjectlistPage";
import CreateProjectlist from "./pages/Projectlist/CreateProjectlist";
import EditProjectlist from "./pages/Projectlist/EditProjectlist";
// Hole Type
import HoleTypePage from "./pages/HoleType/HoleTypePage";
import CreateHoleType from "./pages/HoleType/CreateHoleType";
import EditHoleType from "./pages/HoleType/EditHoleType";
import Materials from "./pages/Materials/Materials";
// Materials
import MaterialsAdd from "./pages/Materials/MaterialsAdd";
import MaterialsEdit from "./pages/Materials/MaterialsEdit";
//bis
import BIS from "./pages/BIS/BISPage";
import CreateBIS from "./pages/BIS/CreateBIS";
import EditBIS from "./pages/BIS/EditBIS";
//processing fee
import ProcessingFeePage from "./pages/ProcessingFee/ProcessingFeePage";
import CreateProcessingFee from "./pages/ProcessingFee/CreateProcessingFee";
import EditProcessingFee from "./pages/ProcessingFee/EditProcessingFee";
//coating
import CoatingPage from "./pages/Coating/CoatingPage";
import CreateCoating from "./pages/Coating/CreateCoating";
import EditCoating from "./pages/Coating/EditCoating";
//  Unit Price Remark
import UnitPriceRemark from "./pages/UnitPriceRemark";
//  MaterialRemarks
import MaterialRemarks from "./pages/MaterialRemarks";
// Draw Page
import DrawPage from "./pages/DrawPage/DrawPage";
// Report List Page
import ReportListPage from "./pages/ReportList/ReportListPage";
import CreateReportList from "./pages/ReportList/CreateReportList";

function App() {
  return (
    <Routes>
      {/* Root path handler - must be first to catch the exact "/" path */}
      <Route path="/" element={<Navigate to="/home" replace />} />

      <Route path="/login" element={<LoginPage />} />

      <Route
        path="/"
        element={
          <ProtectedRoute>
            <MainLayout />
          </ProtectedRoute>
        }
      >
        {/* User Account */}
        <Route path="/user-account" element={<UserAccount />} />
        <Route path="/usermanagement" element={<UserManagement />} />
        <Route path="/useredit" element={<UserEdit />} />
        {/* Customer Master */}
        <Route path="/customer-master" element={<CustomerMaster />} />
        <Route path="/create-customer" element={<CreateCustomerMaster />} />
        <Route
          path="/edit-customer/:orderNo"
          element={<EditCustomerMaster />}
        />
        {/* Rang */}
        <Route path="/rank" element={<RankPage />} />
        <Route path="/create-rank" element={<CreateRank />} />
        <Route path="/edit-rank/:id" element={<EditRank />} />
        {/* Order Import */}
        <Route path="/import-orders" element={<OrderImport />} />
        <Route path="/create-order" element={<CreateOrder />} />
        <Route path="/edit-order/:orderNo" element={<EditOrder />} />
        {/* Project List */}
        <Route path="/project-list" element={<ProjectlistPage />} />
        <Route path="/create-project" element={<CreateProjectlist />} />
        <Route path="/edit-project/:orderNo" element={<EditProjectlist />} />
        {/* Hole Type */}
        <Route path="/hole-type" element={<HoleTypePage />} />
        <Route path="/create-holetype" element={<CreateHoleType />} />
        <Route path="/edit-holetype/:id" element={<EditHoleType />} />
        {/* Materials  */}
        <Route path="/material" element={<Materials />} />
        <Route path="/materialadd" element={<MaterialsAdd />} />
        <Route path="/materialedit/:id" element={<MaterialsEdit />} />
        {/* BIS  */}
        <Route path="/bis" element={<BIS />} />
        <Route path="/create-bis" element={<CreateBIS />} />
        <Route path="/edit-bis/:id" element={<EditBIS />} />
        {/* Proccessing Fee  */}
        <Route path="/processing-fee" element={<ProcessingFeePage />} />
        <Route
          path="/create-processing-fee"
          element={<CreateProcessingFee />}
        />
        <Route
          path="/edit-processing-fee/:id"
          element={<EditProcessingFee />}
        />
        {/* Coating  */}
        <Route path="/coating" element={<CoatingPage />} />
        <Route path="/create-coating" element={<CreateCoating />} />
        <Route path="/edit-coating/:id" element={<EditCoating />} />
        {/* Unit Price Remark  */}
        <Route path="/remarks-unit-price" element={<UnitPriceRemark />} />
        {/* MaterialRemarks  */}
        <Route path="/material-remarks" element={<MaterialRemarks />} />
        {/* Report List Page  */}
        <Route path="/report-list" element={<ReportListPage />} />
      </Route>

      {/* Home */}
      <Route
        path="/home"
        element={
          <ProtectedRoute>
            <HomePage />
          </ProtectedRoute>
        }
      />

      {/* Draw Page */}
      <Route
        path="/draw-page"
        element={
          <ProtectedRoute>
            <UndoProvider>
              <ToolProvider>
                <DrawPage />
              </ToolProvider>
            </UndoProvider>
          </ProtectedRoute>
        }
      />

      <Route path="/create-report" element={<CreateReportList />} />
    </Routes>
  );
}

export default App;
