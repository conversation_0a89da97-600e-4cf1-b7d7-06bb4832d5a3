import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import LanguageSelector from "../components/LanguageSelector";

// React-icons
import { FaChartLine, FaPencilRuler } from "react-icons/fa";

function HomePage() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleMRPClick = () => {
    navigate("/user-account");
  };

  const handleDrawingClick = () => {
    navigate("/report-list");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-4">
      <div className="flex justify-end items-center w-full">
        <LanguageSelector />
      </div>
      
      <div className="flex flex-col items-center justify-center mt-5">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            {t("HomePage.welcome")}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("HomePage.selectOption")}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl">
          <button
            onClick={handleMRPClick}
            className="flex flex-col items-center justify-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-blue-100 h-64"
          >
            <FaChartLine className="text-6xl text-blue-600 mb-4" />
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              {t("HomePage.mrpSystem")}
            </h2>
            <p className="text-gray-600 text-center">
              {t("HomePage.mrpDescription")}
            </p>
          </button>

          <button
            onClick={handleDrawingClick}
            className="flex flex-col items-center justify-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-green-100 h-64"
          >
            <FaPencilRuler className="text-6xl text-green-600 mb-4" />
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              {t("HomePage.reportGenerator")}
            </h2>
            <p className="text-gray-600 text-center">
              {t("HomePage.reportDescription")}
            </p>
          </button>
        </div>

        <div className="mt-16 text-center">
          <p className="text-gray-500">
            {t("HomePage.chooseModule")}
          </p>
        </div>
      </div>
    </div>
  );
}

export default HomePage;
