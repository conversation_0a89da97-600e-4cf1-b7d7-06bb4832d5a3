import { useEffect, useState } from 'react';

/**
 * Custom hook to handle dimension detection and editing
 * @param {Object} stageRef - Reference to the Konva stage
 * @param {Array} shapes - Array of shapes including dimensions
 * @param {Function} setShapes - Function to update shapes
 * @param {Boolean} showToolbar - Whether the toolbar is currently shown
 * @returns {Object} - State and handlers for dimension editing
 */
export function useDimensionDetection(stageRef, shapes, setShapes, showToolbar) {
  const [showKeypad, setShowKeypad] = useState(false);
  const [selectedDimension, setSelectedDimension] = useState(null);

  // Handle stage click detection
  useEffect(() => {
    if (!stageRef.current) return;
    if (showToolbar) return; // Skip if toolbar is shown

    const stage = stageRef.current;

    const handleStageClick = (e) => {
      // Get the clicked point
      const pointerPosition = stage.getPointerPosition();
      if (!pointerPosition) return;
      if (showToolbar) return; // Skip if toolbar is shown

      // Check if we clicked near any dimension
      let foundDimension = null;

      shapes.forEach(shape => {
        if (shape.type === "dimension" && shape.editable !== false) {
          // Only check editable dimensions
          const [x1, y1] = shape.from;
          const [x2, y2] = shape.to;

          // For horizontal dimensions
          if (shape.isVertical === false) {
            // Check if click is near the horizontal line
            const nearLine =
              pointerPosition.y >= y1 - 20 &&
              pointerPosition.y <= y1 + 20 &&
              pointerPosition.x >= x1 &&
              pointerPosition.x <= x2;

            if (nearLine) {
              foundDimension = shape;
            }
          }
          // For vertical dimensions
          else {
            // Check if click is near the vertical line
            const nearLine =
              pointerPosition.x >= x1 - 20 &&
              pointerPosition.x <= x1 + 20 &&
              pointerPosition.y >= y1 &&
              pointerPosition.y <= y2;

            if (nearLine) {
              foundDimension = shape;
            }
          }
        }
      });

      if (foundDimension) {
        // Use setTimeout to ensure this runs after the current event loop
        setTimeout(() => {
          setSelectedDimension(foundDimension);
          setShowKeypad(true);
        }, 0);
      }
    };

    // Remove existing click handlers to prevent duplicates
    stage.off('click');

    // Add the click handler
    stage.on('click', handleStageClick);

    return () => {
      // Clean up event listener
      stage.off('click');
    };
  }, [stageRef, shapes, showToolbar]);

  // Handle document-level click detection (backup approach)
  useEffect(() => {
    // Skip if no stage ref or if toolbar is shown
    if (!stageRef.current || showToolbar) return;

    // Function to handle clicks anywhere on the document
    const handleDocumentClick = (event) => {
      // Skip if toolbar is shown
      if (showToolbar) return;
      
      // Skip if no stage
      const stage = stageRef.current;
      if (!stage) return;

      // Get stage position and dimensions
      const stageBox = stage.container().getBoundingClientRect();

      // Check if click is within stage bounds
      if (
        event.clientX < stageBox.left ||
        event.clientX > stageBox.right ||
        event.clientY < stageBox.top ||
        event.clientY > stageBox.bottom
      ) {
        return; // Click outside stage
      }

      // Convert click position to stage coordinates
      const pointerPosition = {
        x: (event.clientX - stageBox.left) / stage.scaleX(),
        y: (event.clientY - stageBox.top) / stage.scaleY()
      };

      // Find dimension that was clicked
      let foundDimension = null;

      // Check each shape to see if it's a dimension and if it was clicked
      shapes.forEach(shape => {
        if (shape.type === "dimension" && shape.editable !== false) {
          // Only check editable dimensions
          const [x1, y1] = shape.from;
          const [x2, y2] = shape.to;

          // Calculate if click is near the dimension line
          const isNearLine = shape.isVertical
            ? Math.abs(pointerPosition.x - x1) < 20 &&
            pointerPosition.y >= Math.min(y1, y2) &&
            pointerPosition.y <= Math.max(y1, y2)
            : Math.abs(pointerPosition.y - y1) < 20 &&
            pointerPosition.x >= Math.min(x1, x2) &&
            pointerPosition.x <= Math.max(x1, x2);

          if (isNearLine) {
            foundDimension = shape;
          }
        }
      });

      if (foundDimension) {

        // Use requestAnimationFrame to ensure this runs in the next frame
        window.requestAnimationFrame(() => {
          setSelectedDimension(foundDimension);
          setShowKeypad(true);
        });
      }
    };

    // Add global document click listener
    document.addEventListener('click', handleDocumentClick);

    // Clean up
    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [shapes, stageRef, showToolbar]);

  // Handle keypad confirmation
  const handleKeypadConfirm = (value) => {
    
    if (selectedDimension) {
      // Convert to number if possible
      const numericValue = !isNaN(parseFloat(value)) ? parseFloat(value) : value;
      
      // Update shapes
      setShapes(prevShapes => 
        prevShapes.map(shape => {
          if (shape.id === selectedDimension.id && shape.type === "dimension") {
            return {
              ...shape,
              text: numericValue
            };
          }
          return shape;
        })
      );
      
      // Force redraw
      requestAnimationFrame(() => {
        if (stageRef.current) {
          const layer = stageRef.current.findOne("Layer");
          if (layer) {
            layer.batchDraw();
          }
        }
      });
    }
    
    // Close the keypad after confirmation
    setShowKeypad(false);
    setSelectedDimension(null);
  };

  // Return the state and handlers
  return {
    showKeypad,
    setShowKeypad,
    selectedDimension,
    handleKeypadConfirm
  };
}





