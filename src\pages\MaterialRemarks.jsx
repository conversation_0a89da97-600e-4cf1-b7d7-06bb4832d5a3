import React from "react";
import CancelButton from "../components/CancelButton";
import SaveButton from "../components/SaveButton";
import { useTranslation } from "react-i18next";
function MaterialRemarks() {
  const { t } = useTranslation();
  return (
    <div>
      <div className="flex  justify-center items-center w-full">
        <a
          data-testid="text-materialRemarks"
          name="textmaterialRemarks"
          className="text-xl font-bold mb-2 sm:mb-0   px-3 py-3  bg-white"
        >
          {t("remark.remarkMate")}
        </a>
      </div>

      <div className="p-6 rounded-md w-full bg-white flex justify-center ">
        <div className="flex flex-col gap-4">
          <div className="w-full space-y-10">
            <div className="space-y-10">
              {/* Row 1 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {/* กล่อง Material */}
                <div className="flex flex-col sm:flex-row items-center gap-4">
                  <label
                    data-testid="text-materialRemarks-material "
                    name="textmaterialRemarksmaterial "
                    className="font-bold whitespace-nowrap sm:w-auto w-full"
                  >
                    {t("remark.material")}
                  </label>
                  <select
                    data-testid="select-materialRemarks-material "
                    name="selectmaterialRemarksmaterial "
                    className="w-full sm:w-60 border border-gray-300 rounded px-3 py-2 shadow-lg focus:outline-none focus:ring"
                  >
                    <option></option>
                  </select>
                </div>

                {/* กล่อง Code */}
                <div className="flex flex-col sm:flex-row items-center gap-4">
                  <label
                    data-testid="text-materialRemarks-code"
                    name="textmaterialRemarkscode"
                    className="font-bold whitespace-nowrap sm:w-auto w-full"
                  >
                    {t("remark.code")}
                  </label>
                  <input
                    data-testid="input-materialRemarks-code"
                    name="inputmaterialRemarkscode"
                    type="text"
                    className="w-full sm:w-full border border-gray-300 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
                  />
                </div>
              </div>

              {/* Row 2: */}
              <div className="w-full">
                <div className="flex flex-col sm:flex-row items-start">
                  <label
                    data-testid="text-materialRemarks-remark"
                    name="textmaterialRemarksremark"
                    className="font-bold whitespace-nowrap sm:w-28 sm:pr-2 self-start"
                  >
                    {t("remark.remark")}
                  </label>
                  <textarea
                    data-testid="input-materialRemarks-remark"
                    name="inputmaterialRemarksremark"
                    rows="6"
                    className="w-full h-[200px] border border-gray-300 rounded-3xl px-3 py-2 shadow-lg focus:outline-none focus:ring resize-none"
                    placeholder={t("remark.remarkPlace")}
                  ></textarea>
                </div>
              </div>

              <div className="p-6 rounded-md w-full bg-white flex justify-start">
                <div className="flex gap-4">
                  <SaveButton
                    dataTestId="button-materialRemarks-save"
                    name="buttonmaterialRemarkssave"
                  />
                  <CancelButton
                    dataTestId="button-materialRemarks-cancle"
                    name="buttonmaterialRemarkscancle"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MaterialRemarks;
