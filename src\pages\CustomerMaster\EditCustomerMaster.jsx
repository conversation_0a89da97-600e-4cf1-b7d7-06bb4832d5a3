import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";
import { useTranslation } from "react-i18next";

function EditCustomerMaster() {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { customer } = location.state || {};

  const [formData, setFormData] = useState(
    customer || {
      orderNo: "",
      fixedRateAdjustment: "",
      customerAbbr: "",
      siteName: "",
      recipientContact: "",
      deliveryDestination: "",
      shippingClass: "",
      holeDrillingPattern: "",
      processingPattern: "",
    }
  );

  useEffect(() => {
    if (customer) {
      setFormData(customer);
    }
  }, [customer]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = () => {
    console.log("Updated form data:", formData);
    // Add save logic here
    navigate("/customer-master");
  };

  const handleCancel = () => {
    navigate("/customer-master");
  };

  return (
    <div className="max-w-5xl mx-auto">
      <h1
        data-testid="text-customer-edit"
        className="text-lg sm:text-xl md:text-2xl font-bold mb-8"
      >
        {t("customer.customerEdit")}
      </h1>

      <div className="grid grid-cols-2 gap-6">
        {/* First Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-orderNo"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.orderNo")}
          </label>
          <input
            data-testid="input-editFormCustomer-orderNo"
            type="text"
            name="orderNo"
            value={formData.orderNo}
            readOnly
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 bg-gray-100"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-fixedRate"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.fixedRate")}
          </label>
          <input
            data-testid="input-editFormCustomer-fixedRate"
            type="text"
            name="fixedRateAdjustment"
            value={formData.fixedRateAdjustment}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Second Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-customerAbbr"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.customerAbbr")}
          </label>
          <input
            data-testid="input-editFormCustomer-customerAbbr"
            type="text"
            name="customerAbbr"
            value={formData.customerAbbr}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-siteName"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.siteName")}
          </label>
          <input
            data-testid="input-editFormCustomer-siteName"
            type="text"
            name="siteName"
            value={formData.siteName}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Third Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-recipientContact"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.recipientContact")}
          </label>
          <input
            data-testid="input-editFormCustomer-recipientContact"
            type="text"
            name="recipientContact"
            value={formData.recipientContact}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div></div>

        {/* Fourth Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-deliveryDestination"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.deliveryDestination")}
          </label>
          <input
            data-testid="input-editFormCustomer-deliveryDestination"
            type="text"
            name="deliveryDestination"
            value={formData.deliveryDestination}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-shippingClass"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.shippingClass")}
          </label>
          <select
            data-testid="select-editFormCustomer-shippingClass"
            name="shippingClass"
            value={formData.shippingClass}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="class1">Class 1</option>
            <option value="class2">Class 2</option>
            <option value="class3">Class 3</option>
          </select>
        </div>

        {/* Fifth Row */}
        <div>
          <label
            data-testid="text-editFormCustomer-holeDrillingPattern"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("customer.holeDrillingPattern")}
          </label>
          <select
            data-testid="select-editFormCustomer-holeDrillingPattern"
            name="holeDrillingPattern"
            value={formData.holeDrillingPattern}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="cost1">Cost 1</option>
            <option value="cost2">Cost 2</option>
            <option value="cost3">Cost 3</option>
          </select>
        </div>

        <div>
          <label
            data-testid="text-editFormCustomer-processingPattern"
            className="block text-base font-medium text-gray-700 mb-2"
          >
            {t("sidebar.processingPattern")}
          </label>
          <select
            data-testid="input-editFormCustomer-processingPattern"
            name="processingPattern"
            value={formData.processingPattern}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-md shadow-md border border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value=""></option>
            <option value="fee1">Fee 1</option>
            <option value="fee2">Fee 2</option>
            <option value="fee3">Fee 3</option>
          </select>
        </div>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 mt-8">
        <SaveButton
          data-testid="button-editFormCustomer-saveCustomer"
          onClick={handleSave}
        />
        <CancelButton
          data-testid="button-editFormCustomer-saveCustomer"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default EditCustomerMaster;
