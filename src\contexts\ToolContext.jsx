import { createContext, useContext, useState } from "react";

export const ToolContext = createContext();

export const ToolProvider = ({ children }) => {
  const [activeTool, setActiveTool] = useState(null);
  const [showDashLines, setShowDashLines] = useState(false);

  return (
    <ToolContext.Provider value={{ activeTool, setActiveTool, showDashLines, setShowDashLines }}>
      {children}
    </ToolContext.Provider>
  );
};

export const useTool = () => useContext(ToolContext);
