import React from "react";
import { FaRegSave } from "react-icons/fa";
import { useTranslation } from "react-i18next";

function SaveButton({ dataTestId, onClick }) {
  const { t } = useTranslation();

  return (
    <button
      data-testid={dataTestId}
      name="button-save"
      type="submit"
      onClick={onClick}
      className="flex items-center gap-2 px-6 py-2 rounded-md border border-green-500 bg-white hover:bg-green-300"
    >
      <FaRegSave className="text-gray-600 text-xl" />
      <span className="text-gray-600 text-lg">{t("action.save")}</span>
    </button>
  );
}

export default SaveButton;
