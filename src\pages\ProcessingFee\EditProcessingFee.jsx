import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

// import components
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

function EditProcessingFee() {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { processingFeeToEdit } = location.state || {};

  const [formData, setFormData] = useState({
    pattern: "",
    processingItems: "",
    calculationCriteria: "",
    lowestPrice: "",
    priceByMaterial: "",
  });

  useEffect(() => {
    if (processingFeeToEdit) {
      setFormData(processingFeeToEdit);
    }
  }, [processingFeeToEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = () => {
    console.log("Updated form data:", formData);
    navigate("/processing-fee");
  };

  const handleCancel = () => {
    navigate("/processing-fee");
  };

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("processingFee.processingFeeEdit")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton onClick={handleSave} />
          <Canclebutton onClick={handleCancel} />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.processingItems")}
              </label>
              <input
                name="processingItems"
                value={formData.processingItems}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.calculationCriteria")}
              </label>
              <input
                name="calculationCriteria"
                value={formData.calculationCriteria}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.lowestPrice")}
              </label>
              <input
                name="lowestPrice"
                value={formData.lowestPrice}
                onChange={handleChange}
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.priceByMaterial")}
              </label>
              <input
                name="priceByMaterial"
                value={formData.priceByMaterial}
                onChange={handleChange}
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 3 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.pattern")}
              </label>
              <input
                name="pattern"
                value={formData.pattern}
                onChange={handleChange}
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.processingItems")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.calculationCriteria")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.lowestPrice")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.priceByMaterial")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.pattern")}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-[#E9EDF9]">
              <td className="border border-gray-300 px-4 py-1">
                {formData.processingItems}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.calculationCriteria}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.lowestPrice}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.priceByMaterial}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {formData.pattern}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default EditProcessingFee;
