import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";
// React-icons
import { FaSearch } from "react-icons/fa";

// Mock data for customers
const mockCustomers = [
  {
    orderNo: "00200448",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200444",
    customerAbbr: "Ace Plan Co., Ltd.",
    siteName: "Horiuchi Residence",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200443",
    customerAbbr: "Odake Co., Ltd. Kansai Branch",
    siteName: "Energy",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200442",
    customerAbbr: "Hirata Aluminum Co., Ltd.",
    siteName: "Inagaki Building New",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200441",
    customerAbbr: "Nagasaki Co., Ltd.",
    siteName: "LAWSON",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200440",
    customerAbbr: "O-RISE Co., Ltd. Keishi Branch",
    siteName: "TOTO Shiga",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200439",
    customerAbbr: "Sankyo System South Co., Ltd.",
    siteName: "Fukumoto Residence",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200437",
    customerAbbr: "Sanki Glass Building Materials Co., Ltd.",
    siteName: "Yuukai Kobo Day",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200436",
    customerAbbr: "O-RISE Co., Ltd. Head Office",
    siteName: "Medical Corporation Misaki",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
  {
    orderNo: "00200435",
    customerAbbr: "Showa Front Co., Ltd. Osaka",
    siteName: "Kinshuji Temple Gate Materials",
    person: "Okumura",
    email: "<EMAIL>",
    rank: "VIP",
  },
];

function CustomerMasterPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [searchField, setSearchField] = useState("orderNo");
  const customers = mockCustomers;
  const [filteredCustomers, setFilteredCustomers] = useState(mockCustomers);

  // Add pagination state
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 9;

  // Calculate total pages
  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);

  // Get current page data
  const paginatedCustomers = filteredCustomers.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateClick = () => {
    navigate("/create-customer");
  };

  const handleEditClick = (orderNo) => {
    const customer = mockCustomers.find((c) => c.orderNo === orderNo);
    if (customer) {
      navigate(`/edit-customer/${orderNo}`, { state: { customer } });
    }
  };

  useEffect(() => {
    const filtered = customers.filter((item) =>
      item[searchField]?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredCustomers(filtered);
    setCurrentPage(0); // Reset to first page when filter changes
  }, [searchTerm, searchField, customers]);

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-3">
        <h1
          data-testid="text-customer"
          name="textCustomer"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("customer.customer")}
        </h1>

        {/* Create Button */}
        <button
          data-testid="button-createForm-customer"
          name="buttonCreateFormCustomer"
          onClick={handleCreateClick}
          className="btn-create"
        >
          {t("action.create")}
        </button>
      </div>

      {/* Header Section */}
      <div className="flex flex-row items-center mb-3 gap-2">
        {/* Input Search */}
        <div className="relative w-full md:w-[300px] lg:w-[600px]">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            data-testid="search-customer"
            name="searchCustomer"
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={t("customer.placeholder")}
            className="w-full pl-10 pr-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Select Field */}
        <select
          data-testid="select-customerType-search"
          name="selectCustomerTypeSearch"
          value={searchField}
          onChange={(e) => setSearchField(e.target.value)}
          className="w-[200px] px-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="orderNo">{t("customer.orderNo")}</option>
          <option value="customerAbbr">{t("customer.customerAbbr")}</option>
          <option value="siteName">{t("customer.siteName")}</option>
          <option value="person">{t("customer.person")}</option>
        </select>
      </div>

      {/* Table Section */}
      <div className="relative overflow-x-auto shadow-md">
        <table
          data-testid="table-customer"
          name="tableCustomer"
          className="w-full bg-white border-collapse whitespace-nowrap"
        >
          <thead>
            <tr className="bg-[#4472C4] text-white">
              <th className="py-2 px-4 text-left border border-white min-w-[120px]">
                {t("customer.orderNo")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[200px]">
                {t("customer.customerAbbr")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[200px]">
                {t("customer.siteName")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[150px]">
                {t("customer.person")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[150px]">
                {t("customer.email")}
              </th>
              <th className="py-2 px-4 text-left border border-white min-w-[150px]">
                {t("customer.rank")}
              </th>
              <th className="py-2 px-4 text-center border border-white min-w-[120px]">
                {t("userAccount.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedCustomers.map((customer, index) => (
              <tr
                key={customer.orderNo}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="py-1.5 px-4 border border-white">
                  {customer.orderNo}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {customer.customerAbbr}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {customer.siteName}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {customer.person}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {customer.email}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {customer.rank}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  <div className="flex justify-center gap-4">
                    <EditButton
                      dataTestId="button-editForm-customer"
                      onClick={() => handleEditClick(customer.orderNo)}
                    />
                    <DeleteButton dataTestId="button-delete-customer" />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedCustomers.length === 0 && (
              <tr>
                <td colSpan="5" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredCustomers.length}
      />
    </div>
  );
}

export default CustomerMasterPage;
