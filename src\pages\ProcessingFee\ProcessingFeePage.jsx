import { useState } from "react";
import { useNavigate } from "react-router-dom";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";

const mockProcessing = [
  {
    id: 1,
    pattern: "Pattern 1",
    processingItems: "Processing Items 1",
    calculationCriteria: "Calculation Criteria 1",
    lowestPrice: "1000",
    priceByMaterial: "2000",
  },
  {
    id: 2,
    pattern: "Pattern 2",
    processingItems: "Processing Items 2",
    calculationCriteria: "Calculation Criteria 2",
    lowestPrice: "2000",
    priceByMaterial: "3000",
  },
  {
    id: 3,
    pattern: "Pattern 3",
    processingItems: "Processing Items 3",
    calculationCriteria: "Calculation Criteria 3",
    lowestPrice: "3000",
    priceByMaterial: "4000",
  },
  {
    id: 4,
    pattern: "Pattern 4",
    processingItems: "Processing Items 4",
    calculationCriteria: "Calculation Criteria 4",
    lowestPrice: "4000",
    priceByMaterial: "5000",
  },
  {
    id: 5,
    pattern: "Pattern 5",
    processingItems: "Processing Items 5",
    calculationCriteria: "Calculation Criteria 5",
    lowestPrice: "5000",
    priceByMaterial: "6000",
  },
];

function ProcessingFeePage() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(0);
  const [filteredProcessing, setFilteredProcessing] = useState(mockProcessing);

  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredProcessing.length / itemsPerPage);
  const paginatedProcessing = filteredProcessing.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateProcessing = () => {
    navigate("/create-processing-fee");
  };

  const handleEditProcessing = (id) => {
    const processingFeeToEdit = mockProcessing.find((item) => item.id === id);
    navigate(`/edit-processing-fee/${id}`, { state: { processingFeeToEdit } });
  };

  const handleDeleteClick = (id) => {
    console.log("Delete data with id:", id);
  };

  return (
    <div className="w-full">
      <div className="flex items-center mb-3 justify-between">
        <h1
          data-testid="text-processingFee"
          name="textProcessingFee"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("processingFee.processingFee")}
        </h1>
        <button
          data-testid="button-createForm-processingFee"
          name="createProcessingFee"
          type="button"
          onClick={handleCreateProcessing}
          className="btn-create"
        >
          {t("action.create") || "Create"}
        </button>
      </div>

      <div className="overflow-x-auto">
        <table
          data-testid="table-processingFee"
          name="tableProcessingFee"
          className="w-full bg-white border-collapse"
        >
          <thead className="bg-[#4472C4] text-white text-base">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.processingItems")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.calculationCriteria")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.lowestPrice")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.priceByMaterial")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("processingFee.pattern")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-center min-w-[120px]">
                {t("processingFee.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedProcessing.map((item, index) => (
              <tr
                key={item.id}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-white px-4 py-1">
                  {item.processingItems}
                </td>
                <td className="border border-white px-4 py-1">
                  {item.calculationCriteria}
                </td>
                <td className="border border-white px-4 py-1">
                  {item.lowestPrice}
                </td>
                <td className="border border-white px-4 py-1">
                  {item.priceByMaterial}
                </td>
                <td className="border border-white px-4 py-1">
                  {item.pattern}
                </td>
                <td className="border border-white px-4 py-1">
                  <div className="flex justify-center gap-2">
                    <EditButton
                      dataTestId="button-editForm-processingFee"
                      onClick={() => handleEditProcessing(item.id)}
                    />
                    <DeleteButton
                      dataTestId="button-delete-processingFee"
                      onClick={() => handleDeleteClick(item.id)}
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedProcessing.length === 0 && (
              <tr>
                <td colSpan="6" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredProcessing.length}
      />
    </div>
  );
}

export default ProcessingFeePage;
