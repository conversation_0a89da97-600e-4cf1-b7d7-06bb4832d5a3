import { useState } from "react";
import { useNavigate } from "react-router-dom";

// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";
import { useTranslation } from "react-i18next";

const mockBIS = [
  {
    id: 1,
    screwType: "Screw 1",
    color: "Red",
    price: 100,
  },
  {
    id: 2,
    screwType: "Screw 1",
    color: "Blue",
    price: 200,
  },
  {
    id: 3,
    screwType: "Screw 1",
    color: "Green",
    price: 300,
  },
  {
    id: 4,
    screwType: "Screw 1",
    color: "Yellow",
    price: 400,
  },
  {
    id: 5,
    screwType: "Screw 1",
    color: "Orange",
    price: 500,
  },
];

function BISPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(0);
  const [filteredBIS, setFilteredBIS] = useState(mockBIS);

  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredBIS.length / itemsPerPage);
  const paginatedBIS = filteredBIS.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateBIS = () => {
    navigate("/create-bis");
  };

  const handleEditClick = (id) => {
    const bisToEdit = mockBIS.find((item) => item.id === id);
    navigate(`/edit-bis/${id}`, { state: { bisToEdit } });
  };

  const handleDeleteClick = (id) => {
    console.log("Delete bis with id:", id);
  };

  return (
    <div className="w-full">
      <div className="flex items-center mb-3 justify-between">
        <h1
          data-testid="text-bis"
          name="textBis"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("bis.bis")}
        </h1>
        <button
          data-testid="button-createForm-bis"
          name="createBis"
          type="button"
          onClick={handleCreateBIS}
          className="btn-create"
        >
          {t("action.create") || "Create"}
        </button>
      </div>

      <div className="overflow-x-auto">
        <table
          data-testid="table-bis"
          name="tableBis"
          className="w-full bg-white border-collapse"
        >
          <thead className="bg-[#4472C4] text-white text-base">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[50px]">
                {t("bis.No")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("bis.screwType")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("bis.color")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left min-w-[120px]">
                {t("bis.price")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-center min-w-[120px]">
                {t("bis.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedBIS.map((item, index) => (
              <tr
                key={item.id}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-white px-4 py-1">{item.id}</td>
                <td className="border border-white px-4 py-1">
                  {item.screwType}
                </td>
                <td className="border border-white px-4 py-1">{item.color}</td>
                <td className="border border-white px-4 py-1">{item.price}</td>
                <td className="border border-white px-4 py-1">
                  <div className="flex justify-center gap-2">
                    <EditButton
                      dataTestId="button-editForm-bis"
                      onClick={() => handleEditClick(item.id)}
                    />
                    <DeleteButton
                      dataTestId="button-delete-bis"
                      onClick={() => handleDeleteClick(item.id)}
                    />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedBIS.length === 0 && (
              <tr>
                <td colSpan="4" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredBIS.length}
      />
    </div>
  );
}

export default BISPage;
