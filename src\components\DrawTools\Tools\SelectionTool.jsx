import { useEffect, useState } from "react";
import Kon<PERSON> from "konva";
import { useUndo } from "../../../contexts/UndoContext";
import { useTool } from "../../../contexts/ToolContext";
import { v4 as uuidv4 } from "uuid";
import NumericKeypad from "../Popups/NumericKeypad";

function SelectionTool({ stageRef, isActive }) {
  const { addAction, setLines, setArrows, setDoubleArrow, setBoxes } =
    useUndo();
  const { activeTool } = useTool();
  const [showOptions, setShowOptions] = useState(false);
  const [optionsPosition, setOptionsPosition] = useState({ x: 0, y: 0 });
  const [hasCopiedShapes, setHasCopiedShapes] = useState(false);
  const [showCopyFeedback, setShowCopyFeedback] = useState(false);

  // Text editing state for pasted text boxes
  const [showKeypad, setShowKeypad] = useState(false);
  const [selectedTextBox, setSelectedTextBox] = useState(null);
  const [currentTextValue, setCurrentTextValue] = useState("");

  // Text editing functions for pasted text boxes
  const handleTextClick = (textData) => {
    setSelectedTextBox(textData);
    setCurrentTextValue(textData.text.toString());
    setShowKeypad(true);
  };

  const handleKeypadConfirm = (value) => {
    if (selectedTextBox) {
      // Convert to number if possible
      const numericValue = !isNaN(parseFloat(value)) ? parseFloat(value) : value;

      // For pasted text boxes, only update the visual element (not the boxes state)
      // Update the visual text element on the canvas
      if (stageRef && stageRef.current) {
        const stage = stageRef.current;
        const layer = stage.findOne("Layer");
        if (layer) {
          const textGroup = layer.findOne(`#text-box-${selectedTextBox.id}`);
          if (textGroup) {
            const textNode = textGroup.findOne("Text");
            if (textNode) {
              textNode.text(numericValue.toString());
              layer.draw();
            }
          }
        }
      }
    }

    setShowKeypad(false);
    setSelectedTextBox(null);
    setCurrentTextValue("");
  };

  // Check for existing copied shapes on component mount
  useEffect(() => {
    const copiedShapesJSON = localStorage.getItem("copiedShapes");
    setHasCopiedShapes(!!copiedShapesJSON);
  }, []);

  // Add keyboard shortcuts for copy/paste
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Only handle shortcuts when selection tool is active and there are selected shapes
      if (!isActive) return;

      if ((e.ctrlKey || e.metaKey) && e.key === "c") {
        e.preventDefault();
        const layer = stageRef.current?.findOne("Layer");
        const selectedNodes = layer?.find(".selected");
        if (selectedNodes && selectedNodes.length > 0) {
          handleCopy();
        }
      }

      if ((e.ctrlKey || e.metaKey) && e.key === "v") {
        e.preventDefault();
        if (hasCopiedShapes) {
          handlePaste();
        }
      }
    };

    if (isActive) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isActive, hasCopiedShapes]);

  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    let selectionRect = null;
    let selectionStart = null;
    let selectedShapes = [];
    let isSelecting = false;

    let multiDragStartPos = [];

    let lastPointerPos = null;

    // Function to show options menu
    const showOptionsMenu = () => {
      if (selectedShapes.length > 0) {
        // Check if any selected shape is a fullscreen frame
        const hasFullscreenFrame = selectedShapes.some(
          (shape) => shape.hasName && shape.hasName("fullscreen-frame")
        );

        // Don't show options menu if fullscreen frame is selected
        if (hasFullscreenFrame) {
          setShowOptions(false);
          return;
        }

        const box = getSelectionBoundingBox();
        setOptionsPosition({
          x: box.x + box.width / 2,
          y: box.y + box.height + 10,
        });
        setShowOptions(true);
      } else {
        setShowOptions(false);
      }
    };

    // Get bounding box of all selected shapes
    const getSelectionBoundingBox = () => {
      if (selectedShapes.length === 0)
        return { x: 0, y: 0, width: 0, height: 0 };

      let minX = Infinity,
        minY = Infinity,
        maxX = -Infinity,
        maxY = -Infinity;

      selectedShapes.forEach((shape) => {
        const box = shape.getClientRect();
        minX = Math.min(minX, box.x);
        minY = Math.min(minY, box.y);
        maxX = Math.max(maxX, box.x + box.width);
        maxY = Math.max(maxY, box.y + box.height);
      });

      return {
        x: minX,
        y: minY,
        width: maxX - minX,
        height: maxY - minY,
      };
    };

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    if (!isActive) {
      stage.off("mousedown touchstart");
      stage.off("mousemove touchmove");
      stage.off("mouseup touchend");
      return;
    }

    // Helper function to get pointer position for both mouse and touch events
    const getPointerPosition = (evt) => {
      const pos = stage.getPointerPosition();
      if (!pos) return { x: 0, y: 0 };
      return pos;
    };

    const drawSelectionFrames = () => {
      layer.find(".selection-frame").forEach((frame) => frame.destroy());
      selectedShapes.forEach((shape) => {
        // Don't draw selection frames for fullscreen frames to avoid visual conflicts
        if (shape.hasName && shape.hasName("fullscreen-frame")) {
          return;
        }

        const box = shape.getClientRect();
        const frame = new window.Konva.Rect({
          x: box.x,
          y: box.y,
          width: box.width,
          height: box.height,
          stroke: "blue",
          strokeWidth: 1,
          dash: [5, 5],
          name: "selection-frame",
          listening: false,
        });
        layer.add(frame);
        frame.moveToTop();
      });
      layer.draw();

      // Show options menu when selection changes
      showOptionsMenu();
    };

    const makeObjectsDraggable = () => {
      // First, remove all existing drag handlers to avoid duplicates
      const shapes = layer.find(
        "Group, Line, Circle, Rect, Text, Arrow, DoubleArrow, Polyline, Dimension"
      );
      shapes.forEach((shape) => {
        shape.off("dragstart");
        shape.off("dragmove");
        shape.off("dragend");
        shape.off("mouseenter touchstart");
        shape.off("mouseleave touchend");
        shape.off("click tap");
      });

      stage.off("dragstart");
      stage.off("dragmove");
      stage.off("dragend");

      // Now add fresh handlers
      shapes.forEach((shape) => {
        // Skip shapes that are children of a pasted group
        if (
          shape.getParent() &&
          shape.getParent().hasName &&
          shape.getParent().hasName("pasted-group")
        ) {
          return;
        }

        if (
          shape.getParent() &&
          shape.getParent().nodeType !== "Layer" &&
          !shape.hasName("arrow-shape") &&
          !shape.hasName("double-arrow-shape") &&
          !shape.hasName("pasted-group")
        ) {
          return;
        }

        // Make all shapes draggable, including pasted shapes and groups
        const type = getNodeType(shape);
        const lockedTypes = [
          "dash-line",
          "dimension",
          "polyline",
          "circle",
          "fullscreen-frame",
          "rect",
        ];

        if (!lockedTypes.includes(type)) {
          shape.draggable(true);
        } else {
          shape.draggable(false);
        }

        // Mouse/touch enter and leave events
        shape.on("mouseenter touchstart", () => {
          document.body.style.cursor = "pointer";
        });

        shape.on("mouseleave touchend", () => {
          document.body.style.cursor = "default";
          if (!shape.hasName("selected")) {
            layer.find(".selection-frame").forEach((frame) => frame.destroy());
            layer.draw();
          }
        });

        // For pasted groups, add special handlers
        if (shape.hasName && shape.hasName("pasted-group")) {
          shape.on("click tap", (e) => {
            // If not holding shift, clear previous selection
            if (!e.evt.shiftKey) {
              layer.find(".selected").forEach((s) => {
                if (s !== shape) {
                  s.removeName("selected");
                }
              });
            }

            // Toggle selection for this group
            if (shape.hasName("selected")) {
              shape.removeName("selected");
            } else {
              shape.addName("selected");
            }

            // Update selection frames
            drawSelectionFrames();
            e.cancelBubble = true;
          });

          shape.on("dragstart", (e) => {
            shape.moveToTop();
            drawSelectionFrames();
            e.cancelBubble = true;
          });

          shape.on("dragmove", (e) => {
            // Only update selection frames during drag
            drawSelectionFrames();
            e.cancelBubble = true;
          });

          shape.on("dragend", (e) => {
            // Update state for all shapes in the group
            const groupId = shape.id().replace("pasted-group-", "");

            // Update arrows
            shape.find(".arrow-shape").forEach((arrowGroup) => {
              const arrowId = arrowGroup.id().replace("arrow-", "");
              const line = arrowGroup.findOne("Line");
              if (line) {
                const newPoints = line.points();
                setArrows((prev) =>
                  prev.map((arrow) =>
                    arrow.id.toString() === arrowId
                      ? { ...arrow, points: newPoints }
                      : arrow
                  )
                );
              }
            });

            // Update double arrows
            shape.find(".double-arrow-shape").forEach((doubleArrowGroup) => {
              const arrowId = doubleArrowGroup
                .id()
                .replace("double-arrow-", "");
              const line = doubleArrowGroup.findOne("Line");
              if (line) {
                const newPoints = line.points();
                setDoubleArrow((prev) =>
                  prev.map((arrow) =>
                    arrow.id.toString() === arrowId
                      ? { ...arrow, points: newPoints }
                      : arrow
                  )
                );
              }
            });

            // Update lines
            shape.find(".line-shape").forEach((line) => {
              const lineId = line.id().replace("line-", "");
              const newPoints = line.points();
              setLines((prev) =>
                prev.map((l) =>
                  l.id.toString() === lineId ? { ...l, points: newPoints } : l
                )
              );
            });

            // Update text boxes
            shape.find(".text-box-group").forEach((textGroup) => {
              const textId = textGroup.id().replace("text-box-", "");
              const newPos = textGroup.position();
              setBoxes((prev) =>
                prev.map((box) =>
                  box.id.toString() === textId
                    ? { ...box, x: newPos.x, y: newPos.y }
                    : box
                )
              );
            });
          });
        } else if (shape.hasName && shape.hasName("pasted-shape")) {
          // For individual pasted shapes
          shape.on("click tap", (e) => {
            // If not holding shift, clear previous selection
            if (!e.evt.shiftKey) {
              layer.find(".selected").forEach((s) => {
                if (s !== shape) {
                  s.removeName("selected");
                }
              });
            }

            // Toggle selection for this shape
            if (shape.hasName("selected")) {
              shape.removeName("selected");
            } else {
              shape.addName("selected");
            }

            // Update selection frames
            drawSelectionFrames();
            e.cancelBubble = true;
          });

          // Custom drag handlers for pasted shapes
          shape.on("dragstart", (e) => {
            // Add to selection if not already selected
            if (!shape.hasName("selected")) {
              // If not holding shift, clear previous selection
              if (!e.evt.shiftKey) {
                layer
                  .find(".selected")
                  .forEach((s) => s.removeName("selected"));
              }
              shape.addName("selected");
            }

            // Store initial position for all selected pasted shapes
            const selectedPastedShapes = layer.find(".selected.pasted-shape");
            multiDragStartPos = selectedPastedShapes.map((s) => ({
              shape: s,
              x: s.x(),
              y: s.y(),
            }));

            drawSelectionFrames();
            e.cancelBubble = true;
          });
        } else if (shape.hasName && shape.hasName("fullscreen-frame")) {
          // Special handling for fullscreen frames - they can be selected but not moved
          shape.on("click tap", (e) => {
            // If not holding shift, clear previous selection
            if (!e.evt.shiftKey) {
              layer.find(".selected").forEach((s) => {
                if (s !== shape) {
                  s.removeName("selected");
                }
              });
            }

            // Toggle selection for this shape
            if (shape.hasName("selected")) {
              shape.removeName("selected");
            } else {
              shape.addName("selected");
            }

            // Update selection frames
            drawSelectionFrames();
            e.cancelBubble = true;
          });

          // No drag handlers for fullscreen frames - they cannot be moved
        } else {
          // Regular shapes (non-pasted)
          shape.on("click tap", (e) => {
            // If not holding shift, clear previous selection
            if (!e.evt.shiftKey) {
              layer.find(".selected").forEach((s) => {
                if (s !== shape) {
                  s.removeName("selected");
                }
              });
            }

            // Toggle selection for this shape
            if (shape.hasName("selected")) {
              shape.removeName("selected");
            } else {
              shape.addName("selected");
            }

            // Update selection frames
            drawSelectionFrames();
            e.cancelBubble = true;
          });

          shape.on("dragstart", (e) => {
            if (!e.evt.shiftKey) {
              layer.find(".selected").forEach((s) => s.removeName("selected"));
            }

            shape.addName("selected");
            drawSelectionFrames();
          });
        }
      });

      // Stage-level handlers for regular shapes
      stage.on("dragend", () => {
        lastPointerPos = null;
        drawSelectionFrames();

        const updatedArrows = [];
        const updatedDoubleArrows = [];
        const updatedLines = [];

        const nonPastedSelectedShapes = layer
          .find(".selected")
          .filter(
            (s) =>
              !s.hasName ||
              (!s.hasName("pasted-shape") &&
                !s.hasName("pasted-group") &&
                !s.hasName("fullscreen-frame"))
          );

        nonPastedSelectedShapes.forEach((shape) => {
          if (shape.hasName("arrow-shape")) {
            const id = shape.id().replace("arrow-", "");
            const line = shape.findOne("Line");
            if (line) {
              updatedArrows.push({ id, points: line.points() });
            }
          } else if (shape.hasName("double-arrow-shape")) {
            const id = shape.id().replace("double-arrow-", "");
            const line = shape.findOne("Line");
            if (line) {
              updatedDoubleArrows.push({ id, points: line.points() });
            }
          } else if (shape.hasName("line-shape")) {
            const id = shape.id().replace("line-", "");
            if (shape.points) {
              updatedLines.push({ id, points: shape.points() });
            }
          }
        });

        if (updatedArrows.length > 0) {
          setArrows((prev) =>
            prev.map((a) => {
              const updated = updatedArrows.find(
                (u) => u.id === a.id.toString()
              );
              return updated ? { ...a, points: updated.points } : a;
            })
          );
        }

        if (updatedDoubleArrows.length > 0) {
          setDoubleArrow((prev) =>
            prev.map((a) => {
              const updated = updatedDoubleArrows.find(
                (u) => u.id === a.id.toString()
              );
              return updated ? { ...a, points: updated.points } : a;
            })
          );
        }

        if (updatedLines.length > 0) {
          setLines((prev) =>
            prev.map((l) => {
              const updated = updatedLines.find(
                (u) => u.id === l.id.toString()
              );
              return updated ? { ...l, points: updated.points } : l;
            })
          );
        }
      });

      stage.on("dragstart", (e) => {
        const shape = e.target;

        // Skip if this is a pasted shape with custom drag handlers or fullscreen frame
        if (
          shape.hasName &&
          (shape.hasName("pasted-shape") ||
            shape.hasName("pasted-group") ||
            shape.hasName("fullscreen-frame"))
        ) {
          return;
        }

        if (!shape.hasName("selected")) return;

        lastPointerPos = getRelativePointer();

        // Only track non-pasted shapes and non-fullscreen frames
        const nonPastedSelectedShapes = layer
          .find(".selected")
          .filter(
            (s) =>
              !s.hasName ||
              (!s.hasName("pasted-shape") &&
                !s.hasName("pasted-group") &&
                !s.hasName("fullscreen-frame"))
          );

        multiDragStartPos = nonPastedSelectedShapes.map((s) => ({
          shape: s,
          x: s.x(),
          y: s.y(),
        }));
      });

      stage.on("dragmove", (e) => {
        // Skip if this is a pasted shape with custom drag handlers or fullscreen frame
        if (
          e.target.hasName &&
          (e.target.hasName("pasted-shape") ||
            e.target.hasName("pasted-group") ||
            e.target.hasName("fullscreen-frame"))
        ) {
          return;
        }

        if (!lastPointerPos) return;

        const currentPointer = getRelativePointer();
        const dx = currentPointer.x - lastPointerPos.x;
        const dy = currentPointer.y - lastPointerPos.y;

        // Only move non-pasted shapes and non-fullscreen frames
        const nonPastedSelectedShapes = layer
          .find(".selected")
          .filter(
            (s) =>
              !s.hasName ||
              (!s.hasName("pasted-shape") &&
                !s.hasName("pasted-group") &&
                !s.hasName("fullscreen-frame"))
          );

        nonPastedSelectedShapes.forEach((s) => {
          s.x(s.x() + dx);
          s.y(s.y() + dy);
        });

        lastPointerPos = currentPointer;
        layer.batchDraw();
      });
    };

    const handleStageClick = (e) => {
      if (e.target === stage) {
        setShowOptions(false);

        layer.draw();
      }
    };

    makeObjectsDraggable();

    stage.on("click tap", handleStageClick);

    const getRelativePointer = () => {
      const touchPos = stage.getPointerPosition();
      return touchPos ? { x: touchPos.x, y: touchPos.y } : { x: 0, y: 0 };
    };

    const handleStart = (e) => {
      if (e.target !== stage) return;

      isSelecting = true;
      selectionStart = getRelativePointer();

      if (selectionRect) {
        selectionRect.destroy();
      }

      selectionRect = new window.Konva.Rect({
        x: selectionStart.x,
        y: selectionStart.y,
        width: 0,
        height: 0,
        stroke: "rgba(0, 162, 255, 0.75)",
        dash: [4, 4],
        name: "selection-rect",
        listening: false,
      });

      layer.add(selectionRect);
    };

    const handleMove = () => {
      if (!isSelecting || !selectionRect) return;

      const pointer = getRelativePointer();
      const x = Math.min(pointer.x, selectionStart.x);
      const y = Math.min(pointer.y, selectionStart.y);
      const width = Math.abs(pointer.x - selectionStart.x);
      const height = Math.abs(pointer.y - selectionStart.y);

      selectionRect.setAttrs({ x, y, width, height });
      layer.batchDraw();
    };

    const handleEnd = () => {
      if (!selectionRect) return;

      const box = selectionRect.getClientRect();
      selectionRect.destroy();
      selectionRect = null;

      isSelecting = false;

      selectedShapes.forEach((s) => s.removeName("selected"));
      selectedShapes = [];

      const shapes = layer.find(
        "Group, Line, Circle, Rect, Text, Arrow, DoubleArrow, Polyline, Dimension"
      );
      shapes.forEach((shape) => {
        if (window.Konva.Util.haveIntersection(box, shape.getClientRect())) {
          shape.addName("selected");
          selectedShapes.push(shape);
        }
      });

      drawSelectionFrames();
      layer.batchDraw();

      // Show options menu after selection
      showOptionsMenu();
    };

    stage.on("mousedown touchstart", handleStart);
    stage.on("mousemove touchmove", handleMove);
    stage.on("mouseup touchend", handleEnd);

    document.body.style.cursor = "default";

    return () => {
      stage.off("click tap");
      stage.off("mousedown touchstart");
      stage.off("mousemove touchmove");
      stage.off("mouseup touchend");

      document.body.style.cursor = "default";

      const shapes = layer.find(
        "Group, Line, Circle, Rect, Arrow, Text, DoubleArrow, Polyline, Dimension"
      );
      shapes.forEach((shape) => {
        shape.draggable(false);
        shape.off("mouseenter");
        shape.off("mouseleave");
        shape.off("dragstart");
        shape.off("dragmove");
        shape.off("dragend");
      });

      layer.find(".selection-frame").forEach((frame) => frame.destroy());
      layer.draw();
    };
  }, [stageRef, isActive, addAction]);

  // Handle copy function
  const handleCopy = () => {
    if (!stageRef || !stageRef.current) return;
    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    const selectedNodes = layer.find(".selected");
    if (selectedNodes.length > 0) {
      const shapesToCopy = [];

      selectedNodes.forEach((node) => {
        let shapeData = {
          type: node.getClassName().toLowerCase(),
          json: node.toJSON(),
        };

        // For groups or specific shape types, store additional data
        if (
          node.getClassName() === "Group" &&
          node.hasName &&
          node.hasName("circle-shape")
        ) {
          // Handle circle groups from DrawPage
          shapeData.type = "circle";
          const circleNode = node.findOne("Circle");
          if (circleNode) {
            shapeData.data = {
              x: circleNode.x(),
              y: circleNode.y(),
              radius: circleNode.radius(),
              stroke: circleNode.stroke(),
              strokeWidth: circleNode.strokeWidth(),
              fill: circleNode.fill(),
              isCircleVisible: circleNode.visible(),
            };
          }
        } else if (
          node.getClassName() === "Group" &&
          node.hasName &&
          node.hasName("dimension-group")
        ) {
          // Handle dimension groups - these are complex and might not be suitable for copy/paste
          return; // Skip dimension groups
        } else if (node.hasName && node.hasName("text-box-group")) {
          shapeData.type = "text";
          const textNode = node.findOne("Text");
          shapeData.data = {
            text: textNode ? textNode.text() : "",
            fontSize: textNode ? textNode.fontSize() : 18,
            x: node.x(),
            y: node.y(),
          };
        } else if (
          node.getClassName() === "Group" &&
          node.hasName("arrow-shape")
        ) {
          shapeData.type = "arrow";
          const line = node.findOne("Line");
          if (line) {
            // Determine tool type based on dash pattern
            let toolType = "solid";
            if (line.attrs.dash) {
              const dashPattern = line.attrs.dash;
              if (JSON.stringify(dashPattern) === JSON.stringify([6, 3])) {
                toolType = "dashed";
              } else if (
                JSON.stringify(dashPattern) === JSON.stringify([10, 3, 3, 3])
              ) {
                toolType = "dotDash";
              } else if (
                JSON.stringify(dashPattern) ===
                JSON.stringify([10, 3, 3, 3, 3, 3])
              ) {
                toolType = "dotDash2";
              }
            }
            shapeData.data = {
              points: line.points(),
              tool: toolType,
            };
          }
        } else if (
          node.getClassName() === "Group" &&
          node.hasName("double-arrow-shape")
        ) {
          shapeData.type = "double-arrow";
          const line = node.findOne("Line");
          if (line) {
            // Determine tool type based on dash pattern
            let toolType = "solid";
            if (line.attrs.dash) {
              const dashPattern = line.attrs.dash;
              if (JSON.stringify(dashPattern) === JSON.stringify([6, 3])) {
                toolType = "dashed";
              } else if (
                JSON.stringify(dashPattern) === JSON.stringify([10, 3, 3, 3])
              ) {
                toolType = "dotDash";
              } else if (
                JSON.stringify(dashPattern) ===
                JSON.stringify([10, 3, 3, 3, 3, 3])
              ) {
                toolType = "dotDash2";
              }
            }
            shapeData.data = {
              points: line.points(),
              tool: toolType,
            };
          }
        } else if (
          node.getClassName() === "Line" &&
          node.hasName("line-shape")
        ) {
          shapeData.type = "line";
          // Get the associated text node if it exists
          const textNode = layer.findOne(
            `#dimension-text-${node.id().replace("line-", "")}`
          );

          // Determine tool type based on dash pattern
          let toolType = "solid";
          if (node.attrs.dash) {
            const dashPattern = node.attrs.dash;
            if (JSON.stringify(dashPattern) === JSON.stringify([6, 3])) {
              toolType = "dashed";
            } else if (
              JSON.stringify(dashPattern) === JSON.stringify([10, 3, 3, 3])
            ) {
              toolType = "dotDash";
            } else if (
              JSON.stringify(dashPattern) ===
              JSON.stringify([10, 3, 3, 3, 3, 3])
            ) {
              toolType = "dotDash2";
            }
          }

          shapeData.data = {
            points: node.points(),
            tool: toolType,
            text: textNode ? textNode.text() : "",
            textConfig: textNode
              ? {
                  x: textNode.x(),
                  y: textNode.y(),
                  fontSize: textNode.fontSize(),
                  rotation: textNode.rotation(),
                }
              : null,
          };
        } else if (node.getClassName() === "Circle") {
          shapeData.data = {
            x: node.x(),
            y: node.y(),
            radius: node.radius(),
            stroke: node.stroke(),
            strokeWidth: node.strokeWidth(),
            fill: node.fill(),
            isCircleVisible: node.visible(),
          };
        } else if (node.getClassName() === "Arrow") {
          shapeData.data = {
            points: node.points(),
            stroke: node.stroke(),
            strokeWidth: node.strokeWidth(),
          };
        } else if (node.getClassName() === "Rect") {
          shapeData.data = {
            x: node.x(),
            y: node.y(),
            width: node.width(),
            height: node.height(),
            stroke: node.stroke(),
            strokeWidth: node.strokeWidth(),
          };
        } else if (node.getClassName() === "Line") {
          if (
            node.hasName("polyline-shape") ||
            (node.attrs && node.attrs.points && node.attrs.points.length > 4)
          ) {
            shapeData.type = "polyline";
            shapeData.data = {
              points: node.points(),
              stroke: node.stroke() || "black",
              strokeWidth: node.strokeWidth() || 2,
              linkedDimensions: node.getAttr("linkedDimensions") || [],
            };
          } else if (node.hasName("dash-line-shape") || node.getAttr("dash")) {
            shapeData.type = "dash-line";
            shapeData.data = {
              points: node.points(),
              stroke: node.stroke() || "red",
              strokeWidth: node.strokeWidth() || 2,
              dash: node.getAttr("dash") || [5, 5],
            };
          } else {
            shapeData.data = {
              points: node.points(),
              stroke: node.stroke(),
              strokeWidth: node.strokeWidth(),
            };
          }
        } else if (node.getClassName() === "Text") {
          // Handle standalone text elements (text_label type)
          shapeData.type = "text_label";
          shapeData.data = {
            x: node.x(),
            y: node.y(),
            text: node.text(),
            fontSize: node.fontSize() || 18,
            isVisible: node.visible(),
          };
        } else if (node.getClassName() === "Group") {
          // Handle other group types that weren't caught above
          const groupName = node.name();

          // For generic groups (like lineWithLabel), we'll skip them as they're complex
          // and part of the DrawPage shapes array, not the tool-created shapes
          if (!groupName || groupName === "") {
            return; // Skip generic groups
          }

          // Skip this shape as we don't know how to handle it
          return;
        }

        // Only add shapes that have valid data
        if (shapeData.data) {
          shapesToCopy.push(shapeData);
        }
      });

      localStorage.setItem("copiedShapes", JSON.stringify(shapesToCopy));
      setHasCopiedShapes(true);

      // Show copy feedback
      setShowCopyFeedback(true);
      setTimeout(() => setShowCopyFeedback(false), 1000);
    }
  };

  // Handle paste function
  const handlePaste = () => {
    if (!stageRef?.current) return;
    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    const copiedShapesJSON = localStorage.getItem("copiedShapes");
    if (!copiedShapesJSON) {
      setHasCopiedShapes(false);
      return;
    }

    try {
      const copiedShapes = JSON.parse(copiedShapesJSON);

      // Clear current selection
      layer.find(".selected").forEach((shape) => shape.removeName("selected"));
      layer.find(".selection-frame").forEach((frame) => frame.destroy());

      // Calculate bounding boxes and offsets
      const bounds = getBoundingBox(copiedShapes);
      const copiedCenterX = (bounds.minX + bounds.maxX) / 2;
      const copiedCenterY = (bounds.minY + bounds.maxY) / 2;
      const stageCenterX = stage.width() / 2;
      const stageCenterY = stage.height() / 2;
      const offsetX = stageCenterX - copiedCenterX;
      const offsetY = stageCenterY - copiedCenterY; // Create group for pasted shapes
      const groupId = uuidv4();
      const pastedGroup = new window.Konva.Group({
        name: "pasted-group",
        id: `pasted-group-${groupId}`,
        draggable: true,
      });

      // Track newly created shapes for selection
      const newShapes = [];

      // Helper function to get line config based on lineType
      const getLineConfig = (toolType) => {
        switch (toolType) {
          case "dashed":
            return { dash: [6, 3] };
          case "dotDash":
            return { dash: [10, 3, 3, 3] };
          case "dotDash2":
            return { dash: [10, 3, 3, 3, 3, 3] };
          case "solid":
          default:
            return {};
        }
      };

      // Create new shapes from copied data with offset
      copiedShapes.forEach((shapeData) => {
        if (!shapeData || !shapeData.type) {
          return;
        }
        if (!shapeData.data) {
          return;
        }
        const newId = uuidv4();

        let newShape;
        if (shapeData.type === "line" && shapeData.data) {
          // Create the line
          newShape = new window.Konva.Line({
            points: shapeData.data.points.map((p, i) =>
              i % 2 === 0
                ? p - bounds.minX + bounds.minX + offsetX
                : p - bounds.minY + bounds.minY + offsetY
            ),
            stroke: "black",
            strokeWidth: 2,
            name: "line-shape pasted-shape",
            id: `line-${newId}`,
            draggable: false,
            ...getLineConfig(shapeData.data.tool),
          });
          pastedGroup.add(newShape);
          newShapes.push(newShape);

          // If there was text associated with the line, recreate it
          if (shapeData.data.text && shapeData.data.textConfig) {
            const textNode = new window.Konva.Text({
              text: shapeData.data.text,
              fontSize: shapeData.data.textConfig.fontSize || 12,
              fontFamily: "Arial",
              fill: "black",
              x:
                shapeData.data.textConfig.x -
                bounds.minX +
                bounds.minX +
                offsetX,
              y:
                shapeData.data.textConfig.y -
                bounds.minY +
                bounds.minY +
                offsetY,
              rotation: shapeData.data.textConfig.rotation || 0,
              id: `dimension-text-${newId}`,
              name: "dimension-text pasted-shape",
              draggable: false,
            });
            pastedGroup.add(textNode);
            newShapes.push(textNode);
          }
        } else if (shapeData.type === "circle") {
          newShape = new window.Konva.Circle({
            x: shapeData.data.x - bounds.minX + bounds.minX + offsetX,
            y: shapeData.data.y - bounds.minY + bounds.minY + offsetY,
            radius: shapeData.data.radius,
            stroke: shapeData.data.stroke,
            strokeWidth: shapeData.data.strokeWidth,
            fill: shapeData.data.fill,
            visible: shapeData.data.isCircleVisible,
            name: "circle-shape pasted-shape",
            id: `circle-${newId}`,
            draggable: false,
          });
          pastedGroup.add(newShape);
          newShapes.push(newShape);
        } else if (shapeData.type === "rect") {
          newShape = new window.Konva.Rect({
            x: shapeData.data.x - bounds.minX + bounds.minX + offsetX,
            y: shapeData.data.y - bounds.minY + bounds.minY + offsetY,
            width: shapeData.data.width,
            height: shapeData.data.height,
            stroke: shapeData.data.stroke,
            strokeWidth: shapeData.data.strokeWidth,
            name: "rect-shape pasted-shape",
            id: `rect-${newId}`,
            draggable: false,
          });
          pastedGroup.add(newShape);
          newShapes.push(newShape);
        } else if (shapeData.type === "dash-line") {
          newShape = new window.Konva.Line({
            points: shapeData.data.points.map((p, i) =>
              i % 2 === 0
                ? p - bounds.minX + bounds.minX + offsetX
                : p - bounds.minY + bounds.minY + offsetY
            ),
            stroke: shapeData.data.stroke,
            strokeWidth: shapeData.data.strokeWidth,
            dash: shapeData.data.dash,
            name: "dash-line-shape pasted-shape",
            id: `dash-line-${newId}`,
            draggable: false,
          });
          pastedGroup.add(newShape);
          newShapes.push(newShape);
        } else if (shapeData.type === "arrow" && shapeData.data) {
          const newArrowData = {
            ...shapeData.data,
            id: newId,
            points: [...shapeData.data.points],
            groupId: groupId,
          };

          // Apply offset while maintaining relative positions
          const relX = newArrowData.points[0] - bounds.minX;
          const relY = newArrowData.points[1] - bounds.minY;
          const relX2 = newArrowData.points[2] - bounds.minX;
          const relY2 = newArrowData.points[3] - bounds.minY;

          newArrowData.points[0] = relX + bounds.minX + offsetX;
          newArrowData.points[1] = relY + bounds.minY + offsetY;
          newArrowData.points[2] = relX2 + bounds.minX + offsetX;
          newArrowData.points[3] = relY2 + bounds.minY + offsetY;

          // Add to arrows state
          setArrows((prev) => [...prev, newArrowData]);

          // Add to undo history
          addAction({
            type: "add-arrow",
            payload: newArrowData,
          });

          // Create the arrow on the canvas with the pasted-shape class
          if (Konva) {
            const arrowGroup = new Konva.Group({
              name: "arrow-shape pasted-shape",
              id: `arrow-${newId}`,
              draggable: false, // Not draggable individually
            });

            // Apply line type config if available
            const lineConfig = newArrowData.tool
              ? getLineConfig(newArrowData.tool)
              : {};

            // Create the arrow components
            const line = new Konva.Line({
              points: newArrowData.points,
              stroke: "black",
              strokeWidth: 2,
              hitStrokeWidth: 10,
              ...lineConfig, // Apply dash pattern if present
            });

            // Calculate arrow head points
            const [x1, y1, x2, y2] = newArrowData.points;
            const dx = x2 - x1;
            const dy = y2 - y1;
            const angle = Math.atan2(dy, dx);
            const headLength = 12;

            const arrowHead = new Konva.Line({
              points: [
                x2 - headLength * Math.cos(angle - Math.PI / 6),
                y2 - headLength * Math.sin(angle - Math.PI / 6),
                x2,
                y2,
                x2 - headLength * Math.cos(angle + Math.PI / 6),
                y2 - headLength * Math.sin(angle + Math.PI / 6),
              ],
              fill: "black",
              closed: true,
              stroke: "black",
              name: "arrow-head",
              hitStrokeWidth: 10,
            });

            arrowGroup.add(line);
            arrowGroup.add(arrowHead);

            // Add to the pasted group instead of directly to the layer
            pastedGroup.add(arrowGroup);
            newShapes.push(arrowGroup);
          }
        } else if (shapeData.type === "polyline" && shapeData.data) {
          newShape = new window.Konva.Line({
            points: shapeData.data.points.map((p, i) =>
              i % 2 === 0
                ? p - bounds.minX + bounds.minX + offsetX
                : p - bounds.minY + bounds.minY + offsetY
            ),
            stroke: shapeData.data.stroke || "black",
            strokeWidth: shapeData.data.strokeWidth || 2,
            linkedDimensions: shapeData.data.linkedDimensions || [],
            name: "polyline-shape pasted-shape",
            id: `polyline-${newId}`,
            draggable: false,
          });
          pastedGroup.add(newShape);
          newShapes.push(newShape);
        } else if (shapeData.type === "double-arrow" && shapeData.data) {
          const newDoubleArrowData = {
            ...shapeData.data,
            id: newId,
            points: [...shapeData.data.points],
            groupId: groupId,
          };

          // Apply offset while maintaining relative positions
          const relX = newDoubleArrowData.points[0] - bounds.minX;
          const relY = newDoubleArrowData.points[1] - bounds.minY;
          const relX2 = newDoubleArrowData.points[2] - bounds.minX;
          const relY2 = newDoubleArrowData.points[3] - bounds.minY;

          newDoubleArrowData.points[0] = relX + bounds.minX + offsetX;
          newDoubleArrowData.points[1] = relY + bounds.minY + offsetY;
          newDoubleArrowData.points[2] = relX2 + bounds.minX + offsetX;
          newDoubleArrowData.points[3] = relY2 + bounds.minY + offsetY;

          // Add to doubleArrow state
          setDoubleArrow((prev) => [...prev, newDoubleArrowData]);

          // Add to undo history
          addAction({
            type: "add-doubleArrow",
            payload: newDoubleArrowData,
          });

          // Create the double arrow on the canvas
          if (Konva) {
            const doubleArrowGroup = new Konva.Group({
              name: "double-arrow-shape pasted-shape",
              id: `double-arrow-${newId}`,
              draggable: false, // Not draggable individually
            });

            // Apply line type config if available
            const lineConfig = newDoubleArrowData.tool
              ? getLineConfig(newDoubleArrowData.tool)
              : {};

            // Create the arrow components
            const line = new Konva.Line({
              points: newDoubleArrowData.points,
              stroke: "black",
              strokeWidth: 2,
              hitStrokeWidth: 10,
              ...lineConfig, // Apply dash pattern if present
            });

            // Calculate arrow heads
            const [x1, y1, x2, y2] = newDoubleArrowData.points;
            const dx = x2 - x1;
            const dy = y2 - y1;
            const angle = Math.atan2(dy, dx);
            const headLength = 15;

            // End arrow head
            const endArrowHead = new Konva.Line({
              points: [
                x2 - headLength * Math.cos(angle - Math.PI / 6),
                y2 - headLength * Math.sin(angle - Math.PI / 6),
                x2,
                y2,
                x2 - headLength * Math.cos(angle + Math.PI / 6),
                y2 - headLength * Math.sin(angle + Math.PI / 6),
              ],
              stroke: "black",
              strokeWidth: 2,
              name: "end-arrow-head",
              hitStrokeWidth: 10,
            });

            // Start arrow head
            const startArrowHead = new Konva.Line({
              points: [
                x1 + headLength * Math.cos(angle - Math.PI / 6),
                y1 + headLength * Math.sin(angle - Math.PI / 6),
                x1,
                y1,
                x1 + headLength * Math.cos(angle + Math.PI / 6),
                y1 + headLength * Math.sin(angle + Math.PI / 6),
              ],
              stroke: "black",
              strokeWidth: 2,
              name: "start-arrow-head",
              hitStrokeWidth: 10,
            });

            doubleArrowGroup.add(line);
            doubleArrowGroup.add(endArrowHead);
            doubleArrowGroup.add(startArrowHead);

            // Add to the pasted group instead of directly to the layer
            pastedGroup.add(doubleArrowGroup);
            newShapes.push(doubleArrowGroup);
          }
        } else if (shapeData.type === "text" && shapeData.data) {
          const newTextData = {
            ...shapeData.data,
            id: newId,
            x: shapeData.data.x - bounds.minX + bounds.minX + offsetX,
            y: shapeData.data.y - bounds.minY + bounds.minY + offsetY,
            groupId: groupId,
          };

          // Note: Pasted text boxes are handled entirely by SelectionTool
          // They are not added to the boxes state to avoid duplication with TextTool
          console.log("SelectionTool: Creating pasted text box (not adding to state):", newTextData);

          // Create the text box on the canvas as part of the pasted group
          if (Konva) {
            const group = new Konva.Group({
              x: 0,
              y: 0,
              draggable: false, // Not draggable individually
              name: "text-box-group pasted-shape",
              id: `text-box-${newId}`,
            });

            // Position the group at the absolute coordinates
            group.position({
              x: newTextData.x - pastedGroup.x(),
              y: newTextData.y - pastedGroup.y(),
            });

            const rect = new Konva.Rect({
              width: 200,
              height: 60,
              stroke: "transparent", // Make border transparent but keep the shape
              strokeWidth: 1,
              cornerRadius: 4,
              fill: "transparent", // Transparent background
            });

            const text = new Konva.Text({
              text: newTextData.text || "0",
              fontSize: newTextData.fontSize || 18,
              padding: 10,
              fill: "black",
              width: 200,
              height: 60,
              verticalAlign: "middle",
            });

            // Add click handler for text editing when Selection tool is active
            text.on("click", () => {
              // Only allow editing when Selection tool is active (activeTool is null)
              if (activeTool === null) {
                handleTextClick(newTextData);
              }
            });

            rect.on("click", () => {
              // Only allow editing when Selection tool is active
              if (activeTool === null) {
                handleTextClick(newTextData);
              }
            });

            group.add(rect);
            group.add(text);

            // Add to the pasted group instead of directly to the layer
            pastedGroup.add(group);
            newShapes.push(group);
          }
        } else if (shapeData.type === "text_label" && shapeData.data) {
          // Handle standalone text labels (from DrawPage shapes)
          newShape = new window.Konva.Text({
            x: shapeData.data.x - bounds.minX + bounds.minX + offsetX,
            y: shapeData.data.y - bounds.minY + bounds.minY + offsetY,
            text: shapeData.data.text || "",
            fontSize: shapeData.data.fontSize || 18,
            fontFamily: "Arial",
            fill: "black",
            visible: shapeData.data.isVisible !== false,
            name: "text-label pasted-shape",
            id: `text-label-${newId}`,
            draggable: false,
          });
          pastedGroup.add(newShape);
          newShapes.push(newShape);
        } else {
          // Fallback for unhandled shape types - silently skip
        }
      });

      // Add the group to the layer
      layer.add(pastedGroup);

      // Add drag handlers to the group
      pastedGroup.on("dragstart", () => {
        pastedGroup.moveToTop();
      });

      pastedGroup.on("dragend", () => {
        // Update all shape states at once
        const shapeUpdates = {
          arrows: [],
          doubleArrows: [],
          lines: [],
        };

        // Collect all updates first
        pastedGroup.find(".arrow-shape").forEach((arrowGroup) => {
          const line = arrowGroup.findOne("Line");
          if (line) {
            shapeUpdates.arrows.push({
              id: arrowGroup.id().replace("arrow-", ""),
              points: line.points(),
              groupId,
            });
          }
        });

        pastedGroup.find(".double-arrow-shape").forEach((doubleArrowGroup) => {
          const line = doubleArrowGroup.findOne("Line");
          if (line) {
            shapeUpdates.doubleArrows.push({
              id: doubleArrowGroup.id().replace("double-arrow-", ""),
              points: line.points(),
              groupId,
            });
          }
        });

        pastedGroup.find(".line-shape, .polyline-shape").forEach((line) => {
          const id = line.hasName("polyline-shape")
            ? line.id().replace("polyline-", "")
            : line.id().replace("line-", "");
          shapeUpdates.lines.push({
            id,
            points: line.points(),
            groupId,
            linkedDimensions: line.getAttr("linkedDimensions"),
          });
        });

        // Note: Text boxes are handled by TextTool, not as visual elements in pasted groups

        // Apply all updates
        if (shapeUpdates.arrows.length) {
          setArrows((prev) =>
            prev.map((arrow) => {
              const update = shapeUpdates.arrows.find(
                (u) => u.id === arrow.id.toString()
              );
              return update ? { ...arrow, ...update } : arrow;
            })
          );
        }

        if (shapeUpdates.doubleArrows.length) {
          setDoubleArrow((prev) =>
            prev.map((arrow) => {
              const update = shapeUpdates.doubleArrows.find(
                (u) => u.id === arrow.id.toString()
              );
              return update ? { ...arrow, ...update } : arrow;
            })
          );
        }

        if (shapeUpdates.lines.length) {
          setLines((prev) =>
            prev.map((line) => {
              const update = shapeUpdates.lines.find(
                (u) => u.id === line.id.toString()
              );
              return update ? { ...line, ...update } : line;
            })
          );
        }

        // Note: No text box updates needed since pasted text boxes are not in state

        // Note: Pasted text boxes are not in state, so no need to update them
      });

      // Select the group
      pastedGroup.addName("selected");

      // Draw selection frame around the group
      const box = pastedGroup.getClientRect();
      const frame = new window.Konva.Rect({
        x: box.x,
        y: box.y,
        width: box.width,
        height: box.height,
        stroke: "blue",
        strokeWidth: 1,
        dash: [5, 5],
        name: "selection-frame",
        listening: false,
      });
      layer.add(frame);
      frame.moveToTop();

      // Show options menu
      setOptionsPosition({
        x: box.x + box.width / 2,
        y: box.y + box.height + 10,
      });
      setShowOptions(true);

      layer.draw();

      // Automatically clear clipboard after successful paste
      clearCopiedShapes();
    } catch (error) {
      console.error("Error pasting shapes:", error);
    }
  };

  // Function to clear copied shapes
  const clearCopiedShapes = () => {
    localStorage.removeItem("copiedShapes");
    setHasCopiedShapes(false);
  };

  // Handle delete function
  const handleDelete = () => {
    if (!stageRef || !stageRef.current) return;
    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    const selectedNodes = layer.find(".selected");
    if (selectedNodes.length > 0) {
      selectedNodes.forEach((node) => {
        // Identify the types of shapes being deleted to update the appropriate state
        let nodeType = getNodeType(node);
        let nodeId = "";

        // Extract ID based on the determined type
        if (nodeType === "rect") {
          nodeId = node.id().replace("rect-", "");
        } else if (nodeType === "circle") {
          nodeId = node.id().replace("circle-", "");
        } else if (nodeType === "dash-line") {
          nodeId = node.id().replace("dash-line-", "");
        } else if (nodeType === "line") {
          nodeId = node.id().replace("line-", "");
        } else if (nodeType === "arrow") {
          nodeId = node.id().replace("arrow-", "");
        } else if (nodeType === "double-arrow") {
          nodeId = node.id().replace("double-arrow-", "");
        } else if (nodeType === "text") {
          nodeId = node.id().replace("text-box-", "");
        } else if (nodeType === "text_label") {
          nodeId = node.id().replace("text-label-", "");
        }

        // Store for undo with more information about the type
        const deletedNode = {
          json: node.toJSON(),
          parent: node.getParent() ? node.getParent().id() : "layer",
          type: nodeType,
        };

        // Add to undo history with specific type information
        addAction({
          type: "delete-shapes",
          payload: deletedNode,
          shapeType: nodeType,
        });

        // Remove from the appropriate state arrays based on type
        switch (nodeType) {
          case "line":
            setLines((prev) =>
              prev.filter(
                (line) => line && line.id && line.id.toString() !== nodeId
              )
            );
            break;
          case "arrow":
            setArrows((prev) =>
              prev.filter(
                (arrow) => arrow && arrow.id && arrow.id.toString() !== nodeId
              )
            );
            break;
          case "double-arrow":
            setDoubleArrow((prev) =>
              prev.filter(
                (arrow) => arrow && arrow.id && arrow.id.toString() !== nodeId
              )
            );
            break;
          case "text":
            setBoxes((prev) =>
              prev.filter(
                (box) => box && box.id && box.id.toString() !== nodeId
              )
            );
            break;
        }

        // Remove the node from the canvas
        node.destroy();
      });

      layer.find(".selection-frame").forEach((frame) => frame.destroy());
      layer.draw();
      setShowOptions(false);
    }
  };

  // Helper function to determine the type of node
  const getNodeType = (node) => {
    if (!node) return "unknown";

    // First check by ID
    if (node.id) {
      const id = node.id();
      if (id) {
        if (id.includes("rect-")) return "rect";
        if (id.includes("circle-")) return "circle";
        if (id.includes("dash-line-")) return "dash-line";
        if (id.includes("line-")) return "line";
        if (id.includes("polyline-")) return "polyline";
        if (id.includes("dimension-")) return "dimension";
        if (id.includes("arrow-")) return "arrow";
        if (id.includes("double-arrow-")) return "double-arrow";
        if (id.includes("text-box-")) return "text";
        if (id.includes("text-label-")) return "text_label";
      }
    }

    // Then check by name
    if (node.hasName) {
      if (node.hasName("fullscreen-frame")) return "fullscreen-frame";
      if (node.hasName("double-arrow-shape")) return "double-arrow";
      if (node.hasName("arrow-shape")) return "arrow";
      if (node.hasName("line-shape")) return "line";
      if (node.hasName("polyline-shape")) return "polyline";
      if (node.hasName("dimension-group")) return "dimension";
      if (node.hasName("text-box-group")) return "text";
      if (node.hasName("rect-shape")) return "rect";
      if (node.hasName("circle-shape")) return "circle";
      if (node.hasName("dash-line-shape")) return "dash-line";
    }

    // Check by class name
    if (node.getClassName) {
      const className = node.getClassName();

      if (className === "Circle") return "circle";
      if (className === "Rect") return "rect";
      if (className === "Text") {
        // Check if it's part of a text box group
        if (node.getParent && node.getParent()) {
          const parent = node.getParent();
          if (parent.hasName && parent.hasName("text-box-group")) {
            return "text";
          }
        }
        // Otherwise it's a standalone text label
        return "text_label";
      }
      if (className === "Line") {
        // For lines, check if they're part of an arrow or double arrow
        if (node.getParent && node.getParent()) {
          const parent = node.getParent();
          if (parent.hasName) {
            if (parent.hasName("arrow-shape")) return "arrow";
            if (parent.hasName("double-arrow-shape")) return "double-arrow";
            if (parent.hasName("polyline-shape")) return "polyline";
            if (parent.hasName("dimension-group")) return "dimension";
          }
        }

        // Check if this is a dash line
        if (node.getAttr && node.getAttr("dash")) return "dash-line";

        // If it has points, it's a regular line
        if (node.getAttr && node.getAttr("points")) return "line";
      }
    }

    return "unknown";
  };

  // Helper function to calculate bounding box of shapes
  const getBoundingBox = (shapes) => {
    let minX = Infinity,
      minY = Infinity;
    let maxX = -Infinity,
      maxY = -Infinity;

    shapes.forEach((shapeData) => {
      if (!shapeData || !shapeData.data) return;

      if (shapeData.data.points) {
        // Handle shapes with points (lines, arrows, etc.)
        for (let i = 0; i < shapeData.data.points.length; i += 2) {
          minX = Math.min(minX, shapeData.data.points[i]);
          minY = Math.min(minY, shapeData.data.points[i + 1]);
          maxX = Math.max(maxX, shapeData.data.points[i]);
          maxY = Math.max(maxY, shapeData.data.points[i + 1]);
        }
      } else if (shapeData.type === "circle") {
        const radius = shapeData.data.radius;
        minX = Math.min(minX, shapeData.data.x - radius);
        minY = Math.min(minY, shapeData.data.y - radius);
        maxX = Math.max(maxX, shapeData.data.x + radius);
        maxY = Math.max(maxY, shapeData.data.y + radius);
      } else if (shapeData.type === "rect") {
        minX = Math.min(minX, shapeData.data.x);
        minY = Math.min(minY, shapeData.data.y);
        maxX = Math.max(maxX, shapeData.data.x + shapeData.data.width);
        maxY = Math.max(maxY, shapeData.data.y + shapeData.data.height);
      } else if (shapeData.type === "text") {
        minX = Math.min(minX, shapeData.data.x);
        minY = Math.min(minY, shapeData.data.y);
        maxX = Math.max(maxX, shapeData.data.x + 200);
        maxY = Math.max(maxY, shapeData.data.y + 60);
      } else if (shapeData.type === "text_label") {
        // Estimate text dimensions based on font size
        const textWidth =
          (shapeData.data.text || "").length *
          (shapeData.data.fontSize || 18) *
          0.6;
        const textHeight = shapeData.data.fontSize || 18;
        minX = Math.min(minX, shapeData.data.x);
        minY = Math.min(minY, shapeData.data.y);
        maxX = Math.max(maxX, shapeData.data.x + textWidth);
        maxY = Math.max(maxY, shapeData.data.y + textHeight);
      }
    });

    return { minX, minY, maxX, maxY };
  };

  return (
    <>
      {showOptions && (
        <div
          className="absolute bg-white rounded-lg shadow-lg p-2 z-50 flex gap-2"
          style={{
            left: `${optionsPosition.x - 75}px`,
            top: `${optionsPosition.y}px`,
          }}
        >
          <button
            onClick={handleCopy}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 touch-manipulation ${
              showCopyFeedback
                ? "bg-green-100 border border-green-300"
                : hasCopiedShapes
                ? "bg-blue-100 hover:bg-blue-200 border border-blue-300"
                : "bg-blue-50 hover:bg-blue-100"
            }`}
            title={
              hasCopiedShapes
                ? "Copy selected shapes (Ctrl+C) - shapes already copied"
                : "Copy selected shapes (Ctrl+C)"
            }
          >
            {showCopyFeedback ? "Copied!" : "Copy"}
          </button>
          <button
            onClick={hasCopiedShapes ? handlePaste : undefined}
            disabled={!hasCopiedShapes}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 touch-manipulation ${
              hasCopiedShapes
                ? "bg-green-50 hover:bg-green-100 cursor-pointer"
                : "bg-gray-100 text-gray-400 cursor-not-allowed"
            }`}
            title={
              hasCopiedShapes
                ? "Paste copied shapes (Ctrl+V) - will clear clipboard after paste"
                : "Copy shapes first to enable paste (Ctrl+V)"
            }
          >
            Paste
          </button>
          <button
            onClick={handleDelete}
            className="px-4 py-2 bg-red-50 hover:bg-red-100 rounded-md text-sm font-medium transition-colors duration-200 touch-manipulation"
          >
            Delete
          </button>
        </div>
      )}

      {/* Numeric Keypad for editing pasted text boxes */}
      {showKeypad && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-30">
          <NumericKeypad
            initialValue={currentTextValue}
            onConfirm={handleKeypadConfirm}
            onCancel={() => {
              setShowKeypad(false);
              setSelectedTextBox(null);
              setCurrentTextValue("");
            }}
          />
        </div>
      )}
    </>
  );
}

export default SelectionTool;
