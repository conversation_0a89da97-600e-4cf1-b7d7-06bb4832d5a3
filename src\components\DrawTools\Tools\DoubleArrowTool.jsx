import { useEffect, useState } from "react";
import Kon<PERSON> from "konva";
import { useUndo } from "../../../contexts/UndoContext";
import { v4 as uuidv4 } from "uuid";

function DoubleArrowTool({ stageRef, activeTool }) {
  const { addAction, doubleArrow, setDoubleArrow } = useUndo();
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentDoubleArrow, setCurrentDoubleArrow] = useState(null);

  const SNAP_THRESHOLD = 10;
  const HEAD_LENGTH = 15;

  // Handle arrow drawing based on active tool
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    if (activeTool !== "doubleArrow") {
      stage.container().style.cursor = "default";
      stage.off("mousedown touchstart");
      stage.off("mousemove touchmove");
      stage.off("mouseup touchend");
      return;
    }

    stage.container().style.cursor = "crosshair";

    const handleMouseDown = (e) => {
      e.evt.preventDefault();

      const pos = stage.getPointerPosition();
      setIsDrawing(true);

      // Create new arrow with UUID
      const newArrow = {
        id: uuidv4(),
        tool: activeTool,
        points: [pos.x, pos.y, pos.x, pos.y],
      };

      setCurrentDoubleArrow(newArrow);
    };

    const handleMouseMove = (e) => {
      if (!isDrawing) return;
      e.evt.preventDefault();

      const pos = stage.getPointerPosition();
      const [x1, y1] = currentDoubleArrow.points;

      let { x, y } = pos;

      // Snap to grid
      if (Math.abs(x - x1) < SNAP_THRESHOLD) {
        x = x1;
      } else if (Math.abs(y - y1) < SNAP_THRESHOLD) {
        y = y1;
      }

      setCurrentDoubleArrow((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          points: [x1, y1, x, y],
        };
      });
    };

    const handleMouseUp = () => {
      if (!isDrawing || !currentDoubleArrow) return;
      setIsDrawing(false);

      const [x1, y1, x2, y2] = currentDoubleArrow.points;
      const dx = x2 - x1;
      const dy = y2 - y1;
      const length = Math.sqrt(dx * dx + dy * dy);

      if (length < 50) {
        setCurrentDoubleArrow(null);
        return;
      }

      setDoubleArrow((prev) => [...prev, currentDoubleArrow]);
      addAction({
        type: "add-doubleArrow",
        payload: currentDoubleArrow,
        shapeType: "doubleArrow",
      });

      setCurrentDoubleArrow(null);
    };

    // Add event listeners
    stage.on("mousedown touchstart", handleMouseDown);
    stage.on("mousemove touchmove", handleMouseMove);
    stage.on("mouseup touchend", handleMouseUp);

    // Cleanup
    return () => {
      stage.off("mousedown touchstart");
      stage.off("mousemove touchmove");
      stage.off("mouseup touchend");
    };
  }, [activeTool, isDrawing, currentDoubleArrow, doubleArrow]);

  // Draw arrows on the stage
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    // Clear only double arrows that aren't in our state array
    const arrowShapes = layer.find(".double-arrow-shape");
    arrowShapes.forEach((arrow) => {
      const arrowId = arrow.id().replace("double-arrow-", "");
      // Only destroy arrows that aren't in our state array
      if (!doubleArrow.some((a) => a.id.toString() === arrowId)) {
        arrow.destroy();
      }
    });

    // Import Konva dynamically
    if (!Konva) return;

    // Draw all saved double arrows
    doubleArrow.forEach((arrow) => {
      // Check if this arrow already exists on the stage
      const existingArrow = layer.findOne(`#double-arrow-${arrow.id}`);
      if (existingArrow) {
        // Update the arrow with current state data
        const line = existingArrow.findOne("Line");
        if (line) {
          line.points(arrow.points);
          // Update arrow heads
          updateDoubleArrowHeads(existingArrow, arrow.points);
        }
        return;
      }

      drawDoubleArrow(layer, Konva, arrow, `double-arrow-${arrow.id}`);
    });

    // Draw current arrow if in drawing mode
    if (isDrawing && currentDoubleArrow) {
      drawDoubleArrow(layer, Konva, currentDoubleArrow, "current-double-arrow");
    }

    layer.draw();
  }, [doubleArrow, isDrawing, currentDoubleArrow, stageRef]);

  // Helper function to draw a double arrow
  const drawDoubleArrow = (layer, Konva, arrow, id) => {
    const [x1, y1, x2, y2] = arrow.points;

    const doubleArrowGroup = new Konva.Group({
      name: "double-arrow-shape",
      id: id,
      draggable: false,
    });

    // Create the line
    const line = new Konva.Line({
      points: arrow.points,
      stroke: "black",
      strokeWidth: 2,
      hitStrokeWidth: 10,
    });

    // Calculate arrow head points
    const dx = x2 - x1;
    const dy = y2 - y1;
    const angle = Math.atan2(dy, dx);

    // Arrow head size
    const headLength = HEAD_LENGTH;

    // Create arrow head at end point
    const endArrowHead = new Konva.Line({
      points: [
        x2 - headLength * Math.cos(angle - Math.PI / 6),
        y2 - headLength * Math.sin(angle - Math.PI / 6),
        x2,
        y2,
        x2 - headLength * Math.cos(angle + Math.PI / 6),
        y2 - headLength * Math.sin(angle + Math.PI / 6),
      ],
      stroke: "black",
      strokeWidth: 2,
      hitStrokeWidth: 10,
      name: "end-arrow-head",
    });

    // Create arrow head at start point
    const startArrowHead = new Konva.Line({
      points: [
        x1 + headLength * Math.cos(angle - Math.PI / 6),
        y1 + headLength * Math.sin(angle - Math.PI / 6),
        x1,
        y1,
        x1 + headLength * Math.cos(angle + Math.PI / 6),
        y1 + headLength * Math.sin(angle + Math.PI / 6),
      ],
      stroke: "black",
      strokeWidth: 2,
      hitStrokeWidth: 10,
      name: "start-arrow-head",
    });

    // Add line and arrow heads to the group
    doubleArrowGroup.add(line);
    doubleArrowGroup.add(endArrowHead);
    doubleArrowGroup.add(startArrowHead);

    // Add the group to the layer
    layer.add(doubleArrowGroup);
  };

  // Helper function to update arrow heads
  const updateDoubleArrowHeads = (arrowGroup, points) => {
    const [x1, y1, x2, y2] = points;
    const dx = x2 - x1;
    const dy = y2 - y1;
    const angle = Math.atan2(dy, dx);
    const headLength = HEAD_LENGTH;

    const endArrowHead = arrowGroup.findOne(".end-arrow-head");
    if (endArrowHead) {
      endArrowHead.points([
        x2 - headLength * Math.cos(angle - Math.PI / 6),
        y2 - headLength * Math.sin(angle - Math.PI / 6),
        x2,
        y2,
        x2 - headLength * Math.cos(angle + Math.PI / 6),
        y2 - headLength * Math.sin(angle + Math.PI / 6),
      ]);
    }

    const startArrowHead = arrowGroup.findOne(".start-arrow-head");
    if (startArrowHead) {
      startArrowHead.points([
        x1 + headLength * Math.cos(angle - Math.PI / 6),
        y1 + headLength * Math.sin(angle - Math.PI / 6),
        x1,
        y1,
        x1 + headLength * Math.cos(angle + Math.PI / 6),
        y1 + headLength * Math.sin(angle + Math.PI / 6),
      ]);
    }
  };

  return null;
}

export default DoubleArrowTool;
