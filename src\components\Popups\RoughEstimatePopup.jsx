import { useState } from "react";
import { FaSearch, FaCheck } from "react-icons/fa";
import { useTranslation } from "react-i18next";

function RoughEstimatePopup({ isOpen, onClose, projects = [] }) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProject, setSelectedProject] = useState(null);

  if (!isOpen) return null;

  const filteredProjects = projects.filter(
    (project) =>
      project.id.includes(searchTerm) ||
      project.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.projectName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalCount = projects.length;

  const handleSelect = (projectId) => {
    setSelectedProject(selectedProject === projectId ? null : projectId);
  };

  const clearSearch = () => {
    setSearchTerm("");
  };

  const handleCancel = () => {
    setSearchTerm("");
    setSelectedProject(null);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-lg">
        {/* Header */}
        <div className="p-3 border-b text-center">
          <div className="font-bold">
            {t("RoughEstimatePopup.total")} : {totalCount}
          </div>
          <div>
            {t("RoughEstimatePopup.selected")} : {selectedProject ? "1" : "No"}
          </div>
        </div>

        {/* Search */}
        <div className="p-2 border-b flex items-center justify-between">
          <div className="relative flex-grow mr-2">
            <input
              type="text"
              placeholder={t("RoughEstimatePopup.search")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full border rounded-md py-2 px-3 pr-10"
            />
            <FaSearch className="absolute right-3 top-3 text-gray-400" />
          </div>
          <button
            onClick={clearSearch}
            className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md"
          >
            {t("RoughEstimatePopup.cancel")}
          </button>
        </div>

        {/* Project List */}
        <div className="max-h-96 overflow-y-auto">
          {filteredProjects.map((project) => {
            const isSelected = selectedProject === project.id;
            return (
              <div
                key={project.id}
                className={`p-3 border-b hover:bg-gray-100 cursor-pointer ${
                  isSelected ? "bg-gray-300" : ""
                }`}
                onClick={() => handleSelect(project.id)}
              >
                <div className="flex justify-between items-center">
                  <div className="font-bold">{project.id}</div>
                  <div className="flex items-center">
                    {isSelected && <FaCheck className="text-green-500 mr-2" />}
                    <div>{project.company}</div>
                  </div>
                </div>
                <div className="flex items-start mt-1">
                  <div className="text-sm">
                    (ML){" "}
                    <span className="underline">{project.projectName}</span>
                  </div>
                </div>
                <div className="text-sm mt-1">{project.date}</div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="p-3 flex justify-center space-x-4">
          <button
            onClick={handleCancel}
            className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-8 py-2 rounded-full"
          >
            {t("RoughEstimatePopup.cancel")}
          </button>
          <button
            className="bg-green-500 hover:bg-green-600 text-white px-8 py-2 rounded-full"
            onClick={() => {
              onClose();
            }}
          >
            {t("RoughEstimatePopup.ok")}
          </button>
        </div>
      </div>
    </div>
  );
}

export default RoughEstimatePopup;
