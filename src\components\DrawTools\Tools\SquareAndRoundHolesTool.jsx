import { v4 as uuidv4 } from "uuid";

// Define breakpoints for responsive sizing
export const TABLET_BREAKPOINT_MIN = 768;
export const TABLET_BREAKPOINT_MAX = 1024;

export const DEFAULT_SQUARE_SIZE = 350;
export const TABLET_SQUARE_SIZE = 450;

export function createSquareAndRoundHoles(dimensions, stageRef) {
  const HORIZONTAL_OFFSET = 60;
  const VERTICAL_OFFSET = 80;

  const width = window.innerWidth;
  const squareSize =
    width >= TABLET_BREAKPOINT_MIN && width <= TABLET_BREAKPOINT_MAX
      ? TABLET_SQUARE_SIZE
      : DEFAULT_SQUARE_SIZE;

  let centerX = dimensions.width / 2;
  let centerY = dimensions.height / 2;

  if (stageRef && stageRef.current) {
    const stage = stageRef.current;
    const stageWidth = stage.width();
    const stageHeight = stage.height();

    const verticalOffset =
      width > TABLET_BREAKPOINT_MAX ? stageHeight * 0.1 : 0;

    const viewportCenter = {
      x: stageWidth / 2 / stage.scaleX() - stage.x() / stage.scaleX(),
      y:
        (stageHeight / 2 + verticalOffset) / stage.scaleY() -
        stage.y() / stage.scaleY(),
    };

    centerX = viewportCenter.x;
    centerY = viewportCenter.y;
  }

  const x = centerX - squareSize / 2;
  const y = centerY - squareSize / 2;

  // เส้นหลัก (ค่า default 200)
  const baseTop = 200;
  const baseRight = 200;

  const shapes = [
    {
      id: uuidv4(),
      type: "rect",
      x,
      y,
      width: squareSize,
      height: squareSize,
    },
    {
      id: uuidv4(),
      type: "dimension",
      from: [x, y + squareSize + HORIZONTAL_OFFSET],
      to: [x + squareSize, y + squareSize + HORIZONTAL_OFFSET],
      text: baseTop,
      isVertical: false,
      isEditable: true,
    },
    {
      id: uuidv4(),
      type: "dimension",
      from: [x - VERTICAL_OFFSET, y],
      to: [x - VERTICAL_OFFSET, y + squareSize],
      text: baseRight,
      isVertical: true,
      isEditable: true,
      textOffset: 40,
    },
  ];
  return shapes;
}

function getTextDiffOffset() {
  const width = window.innerWidth;
  if (width >= TABLET_BREAKPOINT_MIN && width <= TABLET_BREAKPOINT_MAX) {
    return { x: 210, y: 190 }; // for tablet
  }
  return { x: 160, y: 145 }; // for desktop
}

export function createDimensionLinesOnly(
  dimensions,
  squareSize,
  x,
  y,
  stageRef,
  baseValues = { baseTop: 200, baseRight: 200 },
  dimensionOffsetY = 0,
  diameter = 100
) {
  const { baseTop, baseRight } = baseValues;
  const { x: diffOffsetX, y: diffOffsetY } = getTextDiffOffset();

  const shapes = [];

  // Create a unique group ID for all related shapes
  const groupId = uuidv4();

  let scale = { x: 1, y: 1 };
  let stagePos = { x: 0, y: 0 };
  if (stageRef && stageRef.current) {
    const stage = stageRef.current;
    scale = { x: stage.scaleX(), y: stage.scaleY() };
    stagePos = { x: stage.x(), y: stage.y() };
  }

  const adjustPosition = (pos) => ({
    x: (pos.x - stagePos.x) / scale.x,
    y: (pos.y - stagePos.y) / scale.y,
  });

  const tickLength = 20;
  const tickOffset = 50;

  // Horizontal dimension line
  if (dimensions.topDimension !== undefined) {
    const userTop = dimensions.topDimension;
    const diffTop = baseTop - userTop;

    const fromX = x;
    const fromY = y - dimensionOffsetY;
    const toX = x + squareSize;
    const toY = y - dimensionOffsetY;

    // เส้นหลัก
    shapes.push({
      id: uuidv4(),
      type: "lineWithLabel",
      from: [fromX, fromY],
      to: [toX, toY],
      textMain: userTop,
      textDiff: diffTop,
      isVertical: false,
      stroke: "#000000",
      textMainPos: adjustPosition({ x: (fromX + toX) / 2, y: fromY - 30 }),
      textDiffPos: adjustPosition({
        x: (fromX + toX) / 2 - diffOffsetX,
        y: fromY - 30,
      }),
      scale,
      groupId: groupId, // Add group ID for removal tracking
      isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
    });

    // เส้นขีดด้านซ้าย (จาก fromX, fromY ลงด้านล่าง)
    shapes.push({
      id: uuidv4(),
      type: "tickLine",
      from: [fromX, fromY],
      to: [fromX, fromY + tickLength],
      stroke: "#000000",
      groupId: groupId, // Add group ID for removal tracking
      isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
    });

    // เส้นขีดด้านขวา (จาก toX, toY ลงด้านล่าง)
    shapes.push({
      id: uuidv4(),
      type: "tickLine",
      from: [toX, toY],
      to: [toX, toY + tickLength],
      stroke: "#000000",
      groupId: groupId, // Add group ID for removal tracking
      isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
    });

    // เส้นขีดเพิ่มด้านซ้ายถัดไป (offset ไปทางขวานิดหน่อย)
    shapes.push({
      id: uuidv4(),
      type: "tickLine",
      from: [fromX + tickOffset, fromY],
      to: [fromX + tickOffset, fromY + tickLength],
      stroke: "#000000",
      groupId: groupId, // Add group ID for removal tracking
      isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
    });
  }

  // Vertical dimension line
  if (dimensions.rightDimension !== undefined) {
    const userRight = dimensions.rightDimension;
    const diffRight = baseRight - userRight;

    const fromX = x + squareSize + 80;
    const fromY = y + 70 - dimensionOffsetY;
    const toX = fromX;
    const toY = y + squareSize - dimensionOffsetY + 70;

    // เส้นหลัก
    shapes.push({
      id: uuidv4(),
      type: "lineWithLabel",
      from: [fromX, fromY],
      to: [toX, toY],
      textMain: userRight,
      textDiff: diffRight,
      isVertical: true,
      stroke: "#000000",
      textMainPos: adjustPosition({
        x: fromX + 10,
        y: (fromY + toY) / 2 - 20,
      }),
      textDiffPos: adjustPosition({
        x: fromX + 10,
        y: (fromY + toY) / 2 + diffOffsetY,
      }),
      scale,
      groupId: groupId, // Add group ID for removal tracking
      isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
    });

    // เส้นขีดบนสุด (จาก fromX, fromY ไปทางซ้าย)
    shapes.push({
      id: uuidv4(),
      type: "tickLine",
      from: [fromX, fromY],
      to: [fromX - tickLength, fromY],
      stroke: "#000000",
      groupId: groupId, // Add group ID for removal tracking
      isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
    });

    // เส้นขีดล่างสุด (จาก toX, toY ไปทางซ้าย)
    shapes.push({
      id: uuidv4(),
      type: "tickLine",
      from: [toX, toY],
      to: [toX - tickLength, toY],
      stroke: "#000000",
      groupId: groupId, // Add group ID for removal tracking
      isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
    });

    // เส้นขีดเพิ่มจากตัวล่าง ไปข้างบนเล็กน้อย (offset ไปทางบน)
    shapes.push({
      id: uuidv4(),
      type: "tickLine",
      from: [toX, toY - tickOffset],
      to: [toX - tickLength, toY - tickOffset],
      stroke: "#000000",
      groupId: groupId, // Add group ID for removal tracking
      isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
    });
  }

  const offsetY = 20; 

  const circleRadius = 20;
  const circleX = x + circleRadius + 20; // Center X of the circle
  const circleY = y + squareSize - circleRadius + offsetY; // Center Y of the circle

  shapes.push({
    id: uuidv4(),
    type: "circle",
    x: circleX,
    y: circleY,
    radius: circleRadius,
    stroke: "#000000",
    strokeWidth: 2,
    groupId: groupId, // Add group ID for removal tracking
    isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
  });

  const lineLengthDiagonal = 50;
  const angle = 60 * (Math.PI / 180); // แปลงองศาเป็นเรเดียน

  // ตำแหน่งปลายเส้นเฉียง
  const diagonalEndX = circleX + lineLengthDiagonal * Math.cos(angle);
  const diagonalEndY = circleY + lineLengthDiagonal * Math.sin(angle);

  // ความยาวเส้นแนวนอนต่อจากปลายเส้นเฉียง
  const lineLengthHorizontal = 60;

  // วาดเส้นต่อเนื่องทั้งเส้น (จุดศูนย์กลาง -> ปลายเส้นเฉียง -> เส้นแนวนอน)
  shapes.push({
    id: uuidv4(),
    type: "line",
    points: [
      circleX,
      circleY,
      diagonalEndX,
      diagonalEndY,
      diagonalEndX + lineLengthHorizontal,
      diagonalEndY,
    ],
    stroke: "#000000",
    strokeWidth: 1,
    groupId: groupId, // Add group ID for removal tracking
    isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
  });

  // Add the diameter text for the circle
  shapes.push({
    id: uuidv4(),
    type: "text",
    text: `Ø${diameter}`,
    x: circleX + circleRadius + 10,
    y: circleY + 25,
    fontSize: 16,
    fill: "#000000",
    groupId: groupId, // Add group ID for removal tracking
    isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
  });

  // Add the center lines for the circle
  // Horizontal center line
  shapes.push({
    id: uuidv4(),
    type: "line",
    points: [
      circleX - circleRadius - 10,
      circleY,
      circleX + circleRadius + 10,
      circleY,
    ],
    stroke: "#000000",
    strokeWidth: 1,
    dash: [5, 5],
    groupId: groupId, // Add group ID for removal tracking
    isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
  });

  // Vertical center line
  shapes.push({
    id: uuidv4(),
    type: "line",
    points: [
      circleX,
      circleY - circleRadius - 10,
      circleX,
      circleY + circleRadius + 10,
    ],
    stroke: "#000000",
    strokeWidth: 1,
    dash: [5, 5],
    groupId: groupId, // Add group ID for removal tracking
    isFromSquareAndHoles: true, // Mark as created from SquareAndHoles pattern
  });

  return shapes;
}

// Add a function to handle dimension updates
export function updateDimension(shapes, dimensionId, newText) {
  // Convert to number if possible, otherwise keep as string
  const numericValue = !isNaN(parseFloat(newText))
    ? parseFloat(newText)
    : newText;

  return shapes.map((shape) => {
    if (shape.id === dimensionId && shape.type === "dimension") {
      return {
        ...shape,
        text: numericValue,
      };
    }
    return shape;
  });
}
