import React, { useState } from "react";
import { Outlet } from "react-router-dom";
import Sidebar from "../components/Sidebar";

function MainLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const toggleSidebar = () => setIsSidebarOpen((prev) => !prev);

  return (
    <div className="min-h-screen bg-white">
      <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
      <main
        className={`pt-20 transition-all duration-300 ${
          isSidebarOpen ? "pl-[300px]" : "pl-0"
        }`}
      >
        <div className="px-2 py-1">
          <Outlet />
        </div>
      </main>
    </div>
  );
}

export default MainLayout;
