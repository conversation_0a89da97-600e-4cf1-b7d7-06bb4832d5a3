import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
// Import components
import EditButton from "../../components/EditButton";
import DeleteButton from "../../components/DeleteButton";
import Footer from "../../components/Footer";

// React-icons
import { FaSearch } from "react-icons/fa";

// Mock data for projects
const mockProjects = [
  {
    orderNo: "00200448",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "SA Engineering Co., Ltd.",
    siteName: "Ikagaya Midoricho",
  },
  {
    orderNo: "00200444",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "Ace Plan Co., Ltd.",
    siteName: "Horiuchi Residence",
  },
  {
    orderNo: "00200443",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "Odake Co., Ltd. Kansai Branch",
    siteName: "Energy",
  },
  {
    orderNo: "00200442",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "Hirata Aluminum Co., Ltd.",
    siteName: "Inagaki Building New",
  },
  {
    orderNo: "00200441",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "Nagasaki Co., Ltd.",
    siteName: "LAWSON",
  },
  {
    orderNo: "00200440",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "O-RISE Co., Ltd. Keishi Branch",
    siteName: "TOTO Shiga",
  },
  {
    orderNo: "00200439",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "Sankyo System South Co., Ltd.",
    siteName: "Fukumoto Residence",
  },
  {
    orderNo: "00200437",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "Sanki Glass Building Materials Co., Ltd.",
    siteName: "Yuukai Kobo Day",
  },
  {
    orderNo: "00200436",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "O-RISE Co., Ltd. Head Office",
    siteName: "Medical Corporation Misaki",
  },
  {
    orderNo: "00200435",
    orderDate: "2021-12-10",
    specifiedDeliveryDate: "2021-12-10",
    customerAbbr: "Showa Front Co., Ltd. Osaka",
    siteName: "Kinshuji Temple Gate Materials",
  },
];

function ProjectlistPage() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [searchField, setSearchField] = useState("orderNo");
  const projects = mockProjects;
  const [filteredProjects, setFilteredProjects] = useState(mockProjects);
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 9;

  const totalPages = Math.ceil(filteredProjects.length / itemsPerPage);

  const paginatedProjects = filteredProjects.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  const handleCreateClick = () => {
    navigate("/create-project");
  };

  const handleEditClick = (orderNo) => {
    const project = mockProjects.find((p) => p.orderNo === orderNo);
    if (project) {
      navigate(`/edit-project/${orderNo}`, { state: { project } });
    }
  };

  useEffect(() => {
    const filtered = projects.filter((item) =>
      item[searchField]?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredProjects(filtered);
    setCurrentPage(0);
  }, [searchTerm, searchField, projects]);

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-3">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("project.projectlist")}
        </h1>

        {/* Create Button */}
        <button
          data-testid="button-createForm-projectlist"
          name="createProjectlist"
          type="button"
          onClick={handleCreateClick}
          className="btn-create"
        >
          {t("action.create")}
        </button>
      </div>

      {/* Header Section */}
      <div className="flex flex-row items-center mb-3 gap-2">
        <div className="relative w-full md:w-[300px] lg:w-[600px]">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            data-testid="input-projectlist-search"
            name="projectlistSearch"
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={t("project.placeholder")}
            className="w-full pl-10 pr-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <select
          data-testid="select-projectlist-searchType"
          name="projectlistSearchType"
          value={searchField}
          onChange={(e) => setSearchField(e.target.value)}
          className="w-[200px] px-4 py-2 border border-gray-500 rounded-md shadow-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="orderNo">{t("project.order")}</option>
          <option value="customerAbbr">{t("project.customerAbb")}</option>
          <option value="siteName">{t("project.siteName")}</option>
        </select>
      </div>

      {/* Table Section */}
      <div className="relative overflow-x-auto shadow-md">
        <table
          data-testid="table-projectlist"
          className="w-full bg-white border-collapse whitespace-nowrap"
        >
          <thead>
            <tr className="bg-[#4472C4] text-white">
              <th className="py-3 px-4 text-left border border-white min-w-[120px]">
                {t("project.order")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[150px]">
                {t("project.orderDate")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[150px]">
                {t("project.specDate")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[200px]">
                {t("project.customerAbb")}
              </th>
              <th className="py-3 px-4 text-left border border-white min-w-[200px]">
                {t("project.siteName")}
              </th>
              <th className="py-3 px-4 text-center border border-white min-w-[120px]">
                {t("project.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedProjects.map((project, index) => (
              <tr
                key={project.orderNo}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="py-1.5 px-4 border border-white">
                  {project.orderNo}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {project.orderDate}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {project.specifiedDeliveryDate}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {project.customerAbbr}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  {project.siteName}
                </td>
                <td className="py-1.5 px-4 border border-white">
                  <div className="flex justify-center gap-4">
                    <EditButton
                      onClick={() => handleEditClick(project.orderNo)}
                    />
                    <DeleteButton />
                  </div>
                </td>
              </tr>
            ))}
            {paginatedProjects.length === 0 && (
              <tr>
                <td colSpan="5" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with pagination */}
      <Footer
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        filteredDataLength={filteredProjects.length}
      />
    </div>
  );
}

export default ProjectlistPage;
