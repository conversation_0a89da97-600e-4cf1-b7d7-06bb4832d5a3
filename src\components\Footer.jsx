import {
  FaStepFor<PERSON>,
  FaForward,
  FaStepBackward,
  FaBackward,
} from "react-icons/fa";

function Footer({
  currentPage,
  setCurrentPage,
  totalPages,
  filteredDataLength,
}) {
  const handleNextPage = () => {
    if ((currentPage + 1) * 10 < filteredDataLength) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleFirstPage = () => {
    setCurrentPage(0);
  };

  const handleLastPage = () => {
    setCurrentPage(Math.max(Math.ceil(filteredDataLength / 10) - 1, 0));
  };  

  const pageNumbers = Array.from({ length: totalPages }, (_, i) => i);
  const pageRange = pageNumbers.slice(
    Math.max(currentPage - 2, 0),
    Math.min(currentPage + 2, totalPages)
  );

  return (
    <div className="sticky bottom-0 left-0 right-0 bg-white text-white p-4 flex justify-center z-50 ml-0 ">
      <div className="flex flex-wrap justify-center items-center gap-2 sm:space-x-4">
        <button
          onClick={handleFirstPage}
          disabled={currentPage === 0}
          className="px-3 py-1 sm:px-4 sm:py-2 bg-white text-black border border-gray-600 rounded disabled:bg-gray-300 disabled:text-gray-500 text-xs sm:text-sm"
        >
          <FaStepBackward />
        </button>
        <button
          onClick={handlePreviousPage}
          disabled={currentPage === 0}
          className="px-3 py-1 sm:px-4 sm:py-2 bg-white text-black border border-gray-600 rounded disabled:bg-gray-300 disabled:text-gray-500 text-xs sm:text-sm"
        >
          <FaBackward />
        </button>
        <div className="flex flex-wrap justify-center gap-1 sm:space-x-2">
          {pageRange.map((pageNum) => (
            <button
              key={pageNum}
              onClick={() => setCurrentPage(pageNum)}
              className={`px-3 py-1 sm:px-4 sm:py-2 rounded text-xs sm:text-sm ${
                pageNum === currentPage
                  ? "bg-gray-300 text-black border border-gray-600"
                  : "bg-white text-black border border-gray-600"
              }`}
            >
              {pageNum + 1}
            </button>
          ))}
        </div>
        <button
          onClick={handleNextPage}
          disabled={currentPage >= Math.ceil(filteredDataLength / 10) - 1}
          className="px-3 py-1 sm:px-4 sm:py-2 bg-white text-black border border-gray-600 rounded disabled:bg-gray-300 disabled:text-gray-500 text-xs sm:text-sm"
        >
          <FaForward />
        </button>
        <button
          onClick={handleLastPage}
          disabled={currentPage >= Math.ceil(filteredDataLength / 10) - 1}
          className="px-3 py-1 sm:px-4 sm:py-2 bg-white text-black border border-gray-600 rounded disabled:bg-gray-300 disabled:text-gray-500 text-xs sm:text-sm"
        >
          <FaStepForward />
        </button>
      </div>
    </div>
  );
}

export default Footer;
