import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Select from "react-select";
import { useTranslation } from "react-i18next";

// React-icons
import { AiOutlineClose } from "react-icons/ai";

function OrderEditPopup({
  isOpen,
  onClose,
  formData,
  onChange,
  onSubmit,
  title = "Edit Order",
}) {
  if (!isOpen) return null;

  const { t } = useTranslation();

  // Shipping class options
  const shippingClassOptions = [
    { value: "shipping-f", label: "Shipping F" },
    { value: "shipping-a", label: "Shipping A" },
    { value: "shipping-b", label: "Shipping B" },
  ];

  // Custom styles for Select component
  const customStyles = {
    control: (provided) => ({
      ...provided,
      border: "1px solid #e2e8f0",
      boxShadow: "none",
      "&:hover": {
        border: "1px solid #e2e8f0",
      },
    }),
    indicatorSeparator: () => ({
      display: "none",
    }),
  };

  const handleChange = (field, value) => {
    // Debounce or optimize the onChange call
    onChange(field, value);
  };

  // For text inputs, use a more efficient approach
  const handleTextChange = (e) => {
    const { name, value } = e.target;
    handleChange(name, value);
  };

  // Optimize clear button clicks
  const handleClear = (field) => {
    handleChange(field, "");
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl p-6">
        <h2 className="text-xl font-bold text-center mb-4">{title}</h2>
        <div className="border-t border-gray-300 mb-4"></div>

        <div className="space-y-4">
          {/* Order Date */}
          <div className="flex items-center">
            <label className="w-1/2 font-medium">
              {t("OrderEditPopup.orderDate")}
            </label>
            <div className="relative w-full">
              <DatePicker
                wrapperClassName="w-full"
                selected={formData.orderDate || null}
                onChange={(date) => handleChange("orderDate", date)}
                dateFormat="yyyy/MM/dd"
                className="w-full border border-gray-400 rounded-md py-2 px-3 pr-8 cursor-pointer"
                showIcon
                toggleCalendarOnIconClick
                isClearable
              />
            </div>
          </div>

          {/* Ordering company */}
          <div className="flex items-center">
            <label className="w-1/3 font-medium">
              {t("OrderEditPopup.orderingCompany")}
            </label>
            <div className="w-2/3 relative">
              <input
                type="text"
                name="orderingCompany"
                value={formData.orderingCompany || ""}
                onChange={handleTextChange}
                className="w-full border border-gray-400 rounded-md py-2 px-3 pr-8"
              />
              {formData.orderingCompany && (
                <button
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => handleClear("orderingCompany")}
                >
                  <AiOutlineClose className="text-gray-500" />
                </button>
              )}
            </div>
          </div>

          {/* Desired delivery date */}
          <div className="flex items-center">
            <label className="w-1/2 font-medium">
              {t("OrderEditPopup.deliveryDate")}
            </label>
            <div className="relative w-full">
              <DatePicker
                wrapperClassName="w-full"
                selected={formData.deliveryDate || null}
                onChange={(date) => handleChange("deliveryDate", date)}
                dateFormat="yyyy/MM/dd"
                className="w-full border border-gray-400 rounded-md py-2 px-3 pr-8 cursor-pointer"
                showIcon
                toggleCalendarOnIconClick
                isClearable
              />
            </div>
          </div>

          {/* Contact person */}
          <div className="flex items-center">
            <label className="w-1/3 font-medium">
              {t("OrderEditPopup.contactPerson")}
            </label>
            <div className="w-2/3 relative">
              <input
                type="text"
                value={formData.contactPerson || ""}
                onChange={(e) => handleChange("contactPerson", e.target.value)}
                className="w-full border border-gray-400 rounded-md py-2 px-3 pr-8"
              />
              {formData.contactPerson && (
                <button
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => handleChange("contactPerson", "")}
                >
                  <AiOutlineClose className="text-gray-500" />
                </button>
              )}
            </div>
          </div>

          {/* Delivery destination */}
          <div className="flex items-center">
            <label className="w-1/3 font-medium">
              {t("OrderEditPopup.deliveryDestination")}
            </label>
            <div className="w-2/3 relative">
              <input
                type="text"
                value={formData.deliveryDestination || ""}
                onChange={(e) =>
                  handleChange("deliveryDestination", e.target.value)
                }
                className="w-full border border-gray-400 rounded-md py-2 px-3 pr-8"
              />
              {formData.deliveryDestination && (
                <button
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => handleChange("deliveryDestination", "")}
                >
                  <AiOutlineClose className="text-gray-500" />
                </button>
              )}
            </div>
          </div>

          {/* Shipping class */}
          <div className="flex items-center">
            <label className="w-1/3 font-medium">
              {t("OrderEditPopup.shippingClass")}
            </label>
            <div className="w-2/3">
              <Select
                value={formData.shippingClass}
                onChange={(option) => handleChange("shippingClass", option)}
                options={shippingClassOptions}
                styles={customStyles}
                placeholder={t("OrderEditPopup.shippingClassPlaceholder")}
                className="w-full border border-gray-400 rounded-sm"
                isClearable
              />
            </div>
          </div>

          {/* Site name */}
          <div className="flex items-center">
            <label className="w-1/3 font-medium">
              {t("OrderEditPopup.siteName")}
            </label>
            <div className="w-2/3 relative">
              <input
                type="text"
                name="siteName"
                value={formData.siteName || ""}
                onChange={handleTextChange}
                className="w-full border border-gray-400 rounded-md py-2 px-3 pr-8"
              />
              {formData.siteName && (
                <button
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => handleClear("siteName")}
                >
                  <AiOutlineClose className="text-gray-500" />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-center gap-4 mt-8">
          <button
            onClick={onClose}
            className="bg-gray-300 hover:bg-gray-400 text-black px-12 py-3 rounded-full"
          >
            {t("OrderEditPopup.cancel")}
          </button>
          <button
            onClick={onSubmit}
            className="bg-green-500 hover:bg-green-600 text-white px-12 py-3 rounded-full"
          >
            {t("OrderEditPopup.ok")}
          </button>
        </div>
      </div>
    </div>
  );
}

export default OrderEditPopup;
