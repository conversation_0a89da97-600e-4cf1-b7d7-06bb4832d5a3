import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

// Import components
import SaveButton from "../../components/SaveButton";
import CancelButton from "../../components/CancelButton";

// React-icons
import { FaPlus, FaMinus } from "react-icons/fa";

function CreateRank() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [rankForms, setRankForms] = useState([
    {
      rank: "",
      sellingPrice: "",
      rate: "",
      curvedObject: "",
      cuttingBoard: "",
    },
  ]);

  const handleChange = (index, field, value) => {
    const updatedForms = [...rankForms];
    updatedForms[index][field] = value;
    setRankForms(updatedForms);
  };

  const handleAddForm = () => {
    setRankForms([
      ...rankForms,
      {
        rank: "",
        sellingPrice: "",
        rate: "",
        curvedObject: "",
        cuttingBoard: "",
      },
    ]);
  };

  const handleRemoveForm = (index) => {
    const updatedForms = [...rankForms];
    updatedForms.splice(index, 1);
    setRankForms(updatedForms);
  };

  const handleSave = () => {
    console.log("Saving ranks:", rankForms);
    navigate("/rank");
  };

  const handleCancel = () => {
    navigate("/rank");
  };

  return (
    <div className="max-w-5xl mx-auto p-3">
      <h1
        data-testid="text-createFormRank-rank"
        className="text-2xl font-bold mb-6"
      >
        {t("createAndEditRank.rankAdd")}
      </h1>

      {rankForms.map((form, index) => (
        <div key={index} className="mb-4">
          <div className="flex items-center gap-2">
            {/* Remove button */}
            <div className="pt-6">
              <button
                data-testid="button-createFormRank-deleteForm"
                name="deleteRankForm"
                type="button"
                onClick={() => handleRemoveForm(index)}
                className={`w-10 h-10 flex items-center justify-center rounded-md border border-red-500 text-red-500 ${
                  rankForms.length === 1 ? "opacity-50 cursor-not-allowed" : ""
                }`}
                disabled={rankForms.length === 1}
              >
                <FaMinus />
              </button>
            </div>

            {/* Rank input */}
            <div className="flex flex-col flex-[1] min-w-0">
              <label className="text-sm font-medium text-gray-700 mb-1">
                {t("createAndEditRank.rank")}
              </label>
              <input
                data-testid="input-createFormRank-rank"
                name="rank"
                type="text"
                value={form.rank}
                onChange={(e) => handleChange(index, "rank", e.target.value)}
                className="h-10 border border-gray-500 rounded-md px-3 text-center"
              />
            </div>

            {/* Selling Price input */}
            <div className="flex flex-col flex-[3] min-w-0">
              <label className="text-sm font-medium text-gray-700 mb-1">
                {t("createAndEditRank.sellingPrice")}
              </label>
              <input
                data-testid="input-createFormRank-sellingPrice"
                name="sellingPrice"
                type="number"
                value={form.sellingPrice}
                onChange={(e) =>
                  handleChange(index, "sellingPrice", e.target.value)
                }
                className="h-10 border border-gray-500 rounded-md px-3"
              />
            </div>

            {/* Rate input */}
            <div className="flex flex-col flex-[2] min-w-0">
              <label className="text-sm font-medium text-gray-700 mb-1">
                {t("createAndEditRank.ratePercent")}
              </label>
              <input
                data-testid="input-createFormRank-rate"
                name="rate"
                type="number"
                value={form.rate}
                onChange={(e) => handleChange(index, "rate", e.target.value)}
                className="h-10 border border-gray-500 rounded-md px-3"
              />
            </div>

            {/* curvedObject input */}
            <div className="flex flex-col flex-[2] min-w-0">
              <label className="text-sm font-medium text-gray-700 mb-1">
                {t("createAndEditRank.curvedObject")}
              </label>
              <input
                data-testid="input-createFormRank-curvedObject"
                name="curvedObject"
                type="number"
                value={form.curvedObject}
                onChange={(e) =>
                  handleChange(index, "curvedObject", e.target.value)
                }
                className="h-10 border border-gray-500 rounded-md px-3"
              />
            </div>

            {/* Cutting Board input */}
            <div className="flex flex-col flex-[2] min-w-0">
              <label className="text-sm font-medium text-gray-700 mb-1">
                {t("createAndEditRank.cuttingBoard")}
              </label>
              <input
                data-testid="input-createFormRank-cuttingBoard"
                name="cuttingBoard"
                type="number"
                value={form.cuttingBoard}
                onChange={(e) =>
                  handleChange(index, "cuttingBoard", e.target.value)
                }
                className="h-10 border border-gray-500 rounded-md px-3"
              />
            </div>

            {/* Required indicator */}
            <div className="pt-6">
              <span className="text-xl font-bold">*</span>
            </div>
          </div>
        </div>
      ))}

      {/* Add button */}
      <div className="mb-8">
        <button
          data-testid="button-createFormRank-addRankForm"
          name="addRankForm"
          type="button"
          onClick={handleAddForm}
          className="w-10 h-10 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center text-white shadow-md transition-colors duration-200"
        >
          <FaPlus size={18} />
        </button>
      </div>

      {/* Button Group */}
      <div className="flex gap-4 my-5">
        <SaveButton
          data-testid="button-createFormRank-saveRank"
          onClick={handleSave}
        />
        <CancelButton
          data-testid="button-createFormRank-cancelRank"
          onClick={handleCancel}
        />
      </div>
    </div>
  );
}

export default CreateRank;
