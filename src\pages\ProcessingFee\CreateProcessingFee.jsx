import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

function CreateProcessingFee() {
  const [currentPage, setCurrentPage] = useState(0);
  const [data, setData] = useState([]);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleCancel = () => {
    navigate("/processing-fee");
  };

  const paginatedData = data.slice(currentPage * 10, (currentPage + 1) * 10);

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1 className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20">
          {t("processingFee.processingFeeAdd")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton />
          <Canclebutton onClick={handleCancel} />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.processingItems")}
              </label>
              <input
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.calculationCriteria")}
              </label>
              <input
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.lowestPrice")}
              </label>
              <input
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.priceByMaterial")}
              </label>
              <input
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 3 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label className="block font-bold mb-1">
                {t("processingFee.pattern")}
              </label>
              <input
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto mt-5">
        <table className="w-full bg-white border-collapse text-sm">
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.processingItems")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.calculationCriteria")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.lowestPrice")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.priceByMaterial")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("processingFee.pattern")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((item, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-gray-300 px-4 py-1">
                  {item.column1}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.column2}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.column3}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.column4}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.column5}
                </td>
              </tr>
            ))}
            {paginatedData.length === 0 && (
              <tr>
                <td colSpan="9" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default CreateProcessingFee;
