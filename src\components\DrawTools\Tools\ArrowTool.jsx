import { useEffect, useState } from "react";
import Kon<PERSON> from "konva";
import { useUndo } from "../../../contexts/UndoContext";
import { v4 as uuidv4 } from "uuid";

function ArrowTool({ stageRef, activeTool }) {
  const { addAction, arrows, setArrows } = useUndo();
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentArrow, setCurrentArrow] = useState(null);

  const SNAP_THRESHOLD = 10;

  // Handle arrow drawing based on active tool
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    if (activeTool !== "arrow") {
      stage.container().style.cursor = "default";
      stage.off("mousedown touchstart");
      stage.off("mousemove touchmove");
      stage.off("mouseup touchend");
      return;
    }

    stage.container().style.cursor = "crosshair";

    const handleMouseDown = (e) => {
      e.evt.preventDefault();

      // Get pointer position
      const pos = stage.getPointerPosition();
      setIsDrawing(true);

      // Create new arrow with UUID
      const newArrow = {
        id: uuidv4(),
        tool: activeTool,
        points: [pos.x, pos.y, pos.x, pos.y],
      };

      setCurrentArrow(newArrow);
    };

    const handleMouseMove = (e) => {
      if (!isDrawing) return;
      e.evt.preventDefault();

      const pos = stage.getPointerPosition();
      const [x1, y1] = currentArrow.points;

      let { x, y } = pos;

      // Snap to grid
      if (Math.abs(x - x1) < SNAP_THRESHOLD) {
        x = x1;
      } else if (Math.abs(y - y1) < SNAP_THRESHOLD) {
        y = y1;
      }

      setCurrentArrow((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          points: [x1, y1, x, y],
        };
      });
    };

    const handleMouseUp = () => {
      if (!isDrawing || !currentArrow) return;
      setIsDrawing(false);

      const [x1, y1, x2, y2] = currentArrow.points;
      const dx = x2 - x1;
      const dy = y2 - y1;
      const length = Math.sqrt(dx * dx + dy * dy);

      if (length < 20) {
        setCurrentArrow(null);
        return;
      }

      setArrows((prev) => [...prev, currentArrow]);
      addAction({
        type: "add-arrow",
        payload: currentArrow,
        shapeType: "arrow",
      });

      setCurrentArrow(null);
    };

    // Add event listeners
    stage.on("mousedown touchstart", handleMouseDown);
    stage.on("mousemove touchmove", handleMouseMove);
    stage.on("mouseup touchend", handleMouseUp);

    // Cleanup
    return () => {
      stage.off("mousedown touchstart");
      stage.off("mousemove touchmove");
      stage.off("mouseup touchend");
    };
  }, [activeTool, isDrawing, currentArrow, arrows]);

  // Draw arrows on the stage
  useEffect(() => {
    if (!stageRef || !stageRef.current) return;

    const stage = stageRef.current;
    const layer = stage.findOne("Layer");
    if (!layer) return;

    // Clear only arrows that aren't in our state array
    const arrowShapes = layer.find(".arrow-shape");
    arrowShapes.forEach((arrow) => {
      const arrowId = arrow.id().replace("arrow-", "");
      // Only destroy arrows that aren't in our state array
      if (!arrows.some((a) => a.id.toString() === arrowId)) {
        arrow.destroy();
      }
    });

    // Import Konva dynamically
    if (!Konva) return;

    // Draw all saved arrows
    arrows.forEach((arrow) => {
      // Check if this arrow already exists on the stage
      const existingArrow = layer.findOne(`#arrow-${arrow.id}`);
      if (existingArrow) {
        // Update the arrow with current state data
        const line = existingArrow.findOne("Line");
        if (line) {
          line.points(arrow.points);
          // Update arrow heads
          updateArrowHeads(existingArrow, arrow.points);
        }
        return;
      }

      drawArrow(layer, Konva, arrow, `arrow-${arrow.id}`);
    });

    // Draw current arrow if in drawing mode
    if (isDrawing && currentArrow) {
      drawArrow(layer, Konva, currentArrow, "current-arrow");
    }

    layer.draw();
  }, [arrows, isDrawing, currentArrow, stageRef]);

  // Helper function to draw an arrow
  const drawArrow = (layer, Konva, arrow, id) => {
    const [x1, y1, x2, y2] = arrow.points;

    // Create a group to hold the line and arrowhead
    const arrowGroup = new Konva.Group({
      name: "arrow-shape",
      id: id,
      draggable: false,
    });

    // Create the line
    const line = new Konva.Line({
      points: arrow.points,
      stroke: "black",
      strokeWidth: 2,
      hitStrokeWidth: 10,
    });

    // Calculate arrow head points
    const dx = x2 - x1;
    const dy = y2 - y1;
    const angle = Math.atan2(dy, dx);

    // Arrow head size
    const headLength = 12;

    // Create arrow head at end point
    const arrowHead = new Konva.Line({
      points: [
        x2 - headLength * Math.cos(angle - Math.PI / 6),
        y2 - headLength * Math.sin(angle - Math.PI / 6),
        x2,
        y2,
        x2 - headLength * Math.cos(angle + Math.PI / 6),
        y2 - headLength * Math.sin(angle + Math.PI / 6),
      ],
      fill: "black",
      closed: true,
      stroke: "black",
      name: "arrow-head",
      hitStrokeWidth: 10,
    });

    // Add line and arrow head to the group
    arrowGroup.add(line);
    arrowGroup.add(arrowHead);

    // Add the group to the layer
    layer.add(arrowGroup);
  };

  // Helper function to update arrow heads
  const updateArrowHeads = (arrowGroup, points) => {
    const [x1, y1, x2, y2] = points;
    const dx = x2 - x1;
    const dy = y2 - y1;
    const angle = Math.atan2(dy, dx);
    const headLength = 12;

    const arrowHead = arrowGroup.findOne(".arrow-head");
    if (arrowHead) {
      arrowHead.points([
        x2 - headLength * Math.cos(angle - Math.PI / 6),
        y2 - headLength * Math.sin(angle - Math.PI / 6),
        x2,
        y2,
        x2 - headLength * Math.cos(angle + Math.PI / 6),
        y2 - headLength * Math.sin(angle + Math.PI / 6),
      ]);
    }
  };

  return null;
}

export default ArrowTool;
