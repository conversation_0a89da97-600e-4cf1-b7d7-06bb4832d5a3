import { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

// import components
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

function MaterialsEdit() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const { materialToEdit } = location.state || {};
  const [material, setMaterial] = useState(
    materialToEdit || {
      materialName: "",
      maxLen: "",
      thickness: "",
      curObj: "",
      matetialPrice: "",
    }
  );

  const handleSave = () => {
    console.log("Saving material:", material);
    navigate("/material");
  };

  const handleCancel = () => {
    navigate("/material");
  };

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1
          data-testid="text-createFormMaterial-material"
          name="textcreateFormMaterialmaterial"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("material.materialEdit")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton
            dataTestId="button-createFormMaterial-save"
            name="buttoncreateFormMaterialsave"
            onClick={handleSave}
          />
          <Canclebutton
            dataTestId="button-createFormMaterial-cancle"
            name="buttoncreateFormMaterialcancle"
            onClick={handleCancel}
          />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-name"
                name="textcreateFormMaterialname"
                className="block font-bold mb-1"
              >
                {t("material.materialName")}
              </label>
              <input
                data-testid="input-createFormMaterial-name"
                name="inputcreateFormMaterialname"
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-code"
                name="textcreateFormMaterialcode"
                className="block font-bold mb-1"
              >
                {t("material.curObj")}
              </label>
              <input
                data-testid="input-createFormMaterial-code"
                name="inputcreateFormMaterialcode"
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-length"
                name="textcreateFormMateriallength"
                className="block font-bold mb-1"
              >
                {t("material.maxLen")}
              </label>
              <input
                data-testid="input-createFormMaterial-length"
                name="inputcreateFormMateriallength"
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-price"
                name="textcreateFormMaterialprice"
                className="block font-bold mb-1"
              >
                {t("material.matetialPrice")}
              </label>
              <input
                data-testid="input-createFormMaterial-price"
                name="inputcreateFormMaterialprice"
                type="number"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 3 */}
          <div className="flex flex-col items-start gap-2">
            <label
              data-testid="text-createFormMaterial-thicknesses"
              name="textcreateFormMaterialthicknesses"
              className="block font-bold "
            >
              {t("material.thickness")}
            </label>
            <input
              data-testid="input-createFormMaterial-thicknesses"
              name="inputcreateFormMateriallength"
              type="number"
              className="w-full md:w-96 border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
            />
          </div>
        </div>
      </div>

      <div className="overflow-x-auto mt-5">
        <table
          data-testid="table-createFormMaterial"
          name="tablecreateFormMaterial"
          className="w-full bg-white border-collapse whitespace-nowrap text-sm"
        >
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.materialName")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.maxLen")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.thickness")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.curObj")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.matetialPrice")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-[#E9EDF9]">
              <td className="border border-gray-300 px-4 py-1">
                {material.materialName}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {material.maxLen}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {material.thickness}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {material.curObj}
              </td>
              <td className="border border-gray-300 px-4 py-1">
                {material.matetialPrice}
              </td>
              <td className="border border-gray-300 px-4 py-1">
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default MaterialsEdit;
