import { Routes, Route, Navigate } from "react-router-dom";

// MainLayout for displaying Sidebar
import MainLayout from "../layouts/MainLayout";
import UserAccount from "./pages/UserAccount/UserAccount";
// Import Pages
import LoginPage from "../pages/LoginPage";
import ProtectedRoute from "./ProtectedRoute"; // ตรวจสอบล็อกอิน (จะทำในตัวอย่างถัดไป)

const AppRoutes = () => {
  return (
    <Routes>
      {/* Route สำหรับ Login */}
      <Route path="/login" element={<LoginPage />} />

      {/* Route สำหรับ MainLayout (เฉพาะคนที่ล็อกอิน) */}
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <MainLayout />
          </ProtectedRoute>
        }
      >
        <Route path="/user-account" element={<UserAccount />} />
        {/* เพิ่ม path อื่นๆ ได้ตามต้องการ */}
        <Route path="" element={<Navigate to="/user-account" replace />} />
      </Route>

      {/* catch all - redirect ไป login ถ้าไม่พบเส้นทาง */}
      <Route path="*" element={<Navigate to="/login" />} />
    </Routes>
  );
};

export default AppRoutes;
