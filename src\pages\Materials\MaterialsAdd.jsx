import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Savebutton from "../../components/SaveButton";
import Canclebutton from "../../components/CancelButton";

const data = [];

function MaterialsAdd() {
  const [currentPage, setCurrentPage] = useState(0);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleedit = () => {
    navigate("/useredit");
  };

  const handleCancel = () => {
    navigate("/material");
  };

  const paginatedData = data.slice(currentPage * 10, (currentPage + 1) * 10);

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center w-full">
        <h1
          data-testid="text-createFormMaterial-material"
          name="textcreateFormMaterialmaterial"
          className="text-xl font-bold mb-2 sm:mb-0 sm:ml-0 lg:ml-20"
        >
          {t("material.materialAdd")}
        </h1>

        <div className="flex items-center gap-4 ml-auto mr-4">
          <Savebutton
            dataTestId="button-createFormMaterial-save"
            name="buttoncreateFormMaterialsave"
          />
          <Canclebutton
            dataTestId="button-createFormMaterial-cancle"
            name="buttoncreateFormMaterialcancle"
            onClick={handleCancel}
          />
        </div>
      </div>

      <div className="shadow p-6 rounded-md w-full bg-white border border-gray-400 mt-4">
        <div className="flex flex-col gap-4">
          {/* Row 1 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-name"
                name="textcreateFormMaterialname"
                className="block font-bold mb-1"
              >
                {t("material.materialName")}
              </label>
              <input
                data-testid="input-createFormMaterial-name"
                name="inputcreateFormMaterialname"
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-code"
                name="textcreateFormMaterialcode"
                className="block font-bold mb-1"
              >
                {t("material.curObj")}
              </label>
              <input
                data-testid="input-createFormMaterial-code"
                name="inputcreateFormMaterialcode"
                type="text"
                className="w-full border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 2 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-length"
                name="textcreateFormMateriallength"
                className="block font-bold mb-1"
              >
                {t("material.maxLen")}
              </label>
              <input
                data-testid="input-createFormMaterial-length"
                name="inputcreateFormMateriallength"
                type="number"
                className="w-full md:w-96 border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
            <div className="w-full md:w-96">
              <label
                data-testid="text-createFormMaterial-price"
                name="textcreateFormMaterialprice"
                className="block font-bold mb-1"
              >
                {t("material.matetialPrice")}
              </label>
              <input
                data-testid="input-createFormMaterial-price"
                name="inputcreateFormMaterialprice"
                type="number"
                className="w-full md:w-96 border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
              />
            </div>
          </div>

          {/* Row 3 */}
          <div className="flex flex-col items-start gap-2">
            <label
              data-testid="text-createFormMaterial-thicknesses"
              name="textcreateFormMaterialthicknesses"
              className="block font-bold "
            >
              {t("material.thickness")}
            </label>
            <input
              data-testid="input-createFormMaterial-thicknesses"
              name="inputcreateFormMateriallength"
              type="number"
              className="w-full md:w-96 border border-gray-400 rounded-md px-3 py-2 shadow-lg focus:outline-none focus:ring"
            />
          </div>
        </div>
      </div>

      <div className="overflow-x-auto mt-5">
        <table
          data-testid="table-createFormMaterial"
          name="tablecreateFormMaterial"
          className="w-full bg-white border-collapse whitespace-nowrap text-sm"
        >
          <thead className="bg-[#4472C4] text-white">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.materialName")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.maxLen")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.thickness")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.curObj")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.matetialPrice")}
              </th>
              <th className="border border-gray-300 px-4 py-2 text-left">
                {t("material.operation")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((item, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? "bg-[#E9EDF9]" : "bg-[#D9E1F2]"}
              >
                <td className="border border-gray-300 px-4 py-1">
                  {item.column1}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.column2}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.column3}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.column4}
                </td>
                <td className="border border-gray-300 px-4 py-1">
                  {item.column5}
                </td>
              </tr>
            ))}
            {paginatedData.length === 0 && (
              <tr>
                <td colSpan="9" className="text-center py-4 text-gray-500">
                  No data found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default MaterialsAdd;
